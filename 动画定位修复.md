# 动画定位修复

## 问题描述

用户反馈"没有看到引导动画，把引导动画锚定到按钮中间"

## 问题分析

1. **定位问题**: 之前的动画定位在按钮右侧 (`right: -px(27)`)
2. **可见性问题**: 动画可能被其他元素遮挡或定位不正确
3. **用户需求**: 希望动画锚定到按钮的正中间

## 修复方案

### 1. 修改定位方式

**之前**:
```typescript
style={{
  position: 'absolute',
  right: -px(27),  // 定位在右侧
  top: '50%',
  marginTop: -px(27),
  // ...
}}
```

**现在**:
```typescript
style={{
  position: 'absolute',
  left: '50%',     // 定位在中间
  top: '50%',
  marginLeft: -px(27),  // 居中偏移
  marginTop: -px(27),
  // ...
}}
```

### 2. 添加调试背景色

在开发模式下添加红色半透明背景，方便确认动画容器位置：
```typescript
backgroundColor: __DEV__ ? 'rgba(255, 0, 0, 0.3)' : 'transparent',
```

### 3. 添加调试日志

添加控制台日志来确认动画状态：
```typescript
console.log('显示引导动画，状态:', { 
  success: listenTaskInfo.success, 
  showGuide: showListenTaskGuide 
})
```

## 技术细节

### 定位原理

- 使用 `left: '50%'` 将动画容器的左边缘定位到按钮宽度的50%位置
- 使用 `marginLeft: -px(27)` 将动画容器向左偏移自身宽度的一半，实现居中
- 使用 `top: '50%'` 和 `marginTop: -px(27)` 实现垂直居中

### 动画容器尺寸

- 宽度: `px(54)` (54像素)
- 高度: `px(54)` (54像素)
- 偏移量: `px(27)` (27像素，即容器尺寸的一半)

## 预期效果

1. **居中显示**: 动画现在会显示在按钮的正中间
2. **可见性确认**: 开发模式下会显示红色背景，方便确认位置
3. **调试信息**: 控制台会输出动画状态信息

## 测试步骤

1. 启动项目
2. 进入听书任务模块
3. 等待1.5秒后观察按钮中间是否出现动画
4. 检查控制台日志确认状态
5. 在开发模式下确认红色背景位置

## 文件修改

**文件**: `src/components/CoinCenter/ListenTask/index.tsx`

**修改内容**:
- 动画定位从右侧改为中间
- 添加调试背景色
- 添加调试日志

## 完成状态

✅ **定位修复**: 动画现在锚定到按钮中间
✅ **调试功能**: 添加了开发模式下的背景色和日志
✅ **代码更新**: 相关代码已更新并保存
