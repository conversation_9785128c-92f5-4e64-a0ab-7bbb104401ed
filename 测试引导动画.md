# 听书任务引导动画测试指南

## 测试步骤

### 1. 开发模式测试
1. 确保应用运行在开发模式（__DEV__ 为 true）
2. 进入福利中心页面
3. 找到听书任务模块
4. 点击右上角的"显示引导"按钮
5. 观察是否在听书任务按钮右侧显示手势引导动画

### 2. 完整流程测试
1. 进入福利中心页面
2. 找到新人礼包模块
3. 点击听书任务按钮
4. 观察是否：
   - 显示"正在定位到听书模块..."提示
   - 页面自动滚动到听书任务模块
   - 在听书任务按钮位置显示手势引导动画
   - 3秒后动画自动消失

### 3. 动画效果检查
- 动画应该显示在听书任务按钮的右侧
- 动画大小应该是 54x54 像素
- 动画应该循环播放
- 动画应该是手势引导效果（参考大转盘动画）

## 可能的问题和解决方案

### 问题1：看不到引导动画
**原因**：动画位置计算错误或zIndex不够高
**解决**：检查动画的position和zIndex设置

### 问题2：动画位置不准确
**原因**：相对定位计算错误
**解决**：调整动画的right和top值

### 问题3：动画不播放
**原因**：lottie动画源数据有问题
**解决**：检查GUIDE_HAND_LOTTIE数据是否正确

### 问题4：事件监听不工作
**原因**：GlobalEventEmitter事件没有正确触发
**解决**：检查事件监听器的注册和移除

## 调试信息
- 开发模式下会显示测试按钮
- 可以通过测试按钮手动触发/隐藏动画
- 控制台会输出相关调试信息
