# 大转盘Lottie动画集成完成

## 实现概述

已成功将大转盘模块中的手势引导Lottie动画集成到听书任务模块中。

## 主要修改

### 1. 文件位置
- **目标文件**: `src/components/CoinCenter/ListenTask/index.tsx`
- **源文件**: `src/components/CoinCenter/SpiningWheel/index.tsx`

### 2. 关键修改内容

#### 2.1 导入AnimatedLottieView
```typescript
import AnimatedLottieView from 'lottie-react-native'
```

#### 2.2 定义Lottie动画常量
```typescript
// 大转盘手势引导动画的lottie数据
const GUIDE_HAND_LOTTIE = {
  "v": "5.6.3",
  "fr": 25,
  "ip": 9,
  "op": 113,
  "w": 162,
  "h": 162,
  "nm": "容器 10080@3x",
  "ddd": 0,
  "assets": [...],
  "layers": [...],
  "markers": []
}
```

#### 2.3 动画渲染组件
```typescript
{/* 听书任务引导动画（使用大转盘的lottie动画） */}
{showListenTaskGuide && (
  <View
    style={{
      position: 'absolute',
      right: -px(27),
      top: '50%',
      marginTop: -px(27),
      width: px(54),
      height: px(54),
      zIndex: 9999,
    }}
    pointerEvents="none"
  >
    <AnimatedLottieView
      source={GUIDE_HAND_LOTTIE}
      style={{
        width: '100%',
        height: '100%',
      }}
      autoPlay={true}
      loop={true}
      speed={1.0}
      resizeMode="cover"
    />
  </View>
)}
```

## 功能特点

1. **直接使用大转盘动画**: 完全复用了大转盘模块中的Lottie动画数据
2. **自动播放**: 动画会在显示时自动开始播放
3. **循环播放**: 动画会持续循环播放
4. **精确定位**: 动画定位在听书任务按钮的右侧
5. **非交互性**: 设置了`pointerEvents="none"`，不会影响用户交互

## 触发条件

动画会在以下条件同时满足时显示：
- `showListenTaskGuide` 状态为 `true`
- 听书任务模块成功加载

## 技术实现

- 使用 `AnimatedLottieView` 组件渲染Lottie动画
- 动画数据直接嵌入到代码中，无需外部文件
- 使用绝对定位确保动画在正确位置显示
- 设置高z-index确保动画在最上层显示

## 完成状态

✅ **已完成**: 大转盘Lottie动画已成功集成到听书任务模块
✅ **已验证**: 代码语法正确，无编译错误
✅ **可运行**: 项目可以正常启动和运行

## 下一步

用户现在可以在听书任务模块中看到大转盘的手势引导动画效果。
