# 听书任务引导动画调试指南

## 当前状态
我已经添加了以下调试功能：

1. **红色背景**: 动画容器有红色半透明背景，便于识别位置
2. **调试日志**: 控制台会输出事件监听和状态变化
3. **状态显示**: 右上角显示当前动画状态
4. **测试按钮**: 可以手动触发/隐藏动画
5. **简单动画**: 使用简单的红色圆形动画替代复杂动画

## 调试步骤

### 1. 检查开发模式
确保应用运行在开发模式（__DEV__ 为 true）

### 2. 查看调试信息
- 右上角应该显示"状态: 隐藏"或"状态: 显示"
- 点击"显示引导"按钮，状态应该变为"显示"

### 3. 检查控制台日志
在开发者工具中查看控制台，应该看到：
```
收到showListenTaskGuide事件
设置showListenTaskGuide为true
设置showListenTaskGuide为false
```

### 4. 查看红色背景
如果动画容器显示，应该能看到红色半透明背景

### 5. 测试手动触发
点击右上角的"显示引导"按钮，观察：
- 状态是否变化
- 是否看到红色背景
- 是否看到红色圆形动画

## 可能的问题和解决方案

### 问题1: 看不到任何调试信息
**原因**: 可能不在开发模式
**解决**: 确保 __DEV__ 为 true

### 问题2: 状态显示但看不到动画
**原因**: 动画位置可能被遮挡
**解决**: 检查 zIndex 和父容器样式

### 问题3: 控制台没有日志
**原因**: 事件监听器没有正确注册
**解决**: 检查 GlobalEventEmitter 是否正确导入

### 问题4: 看到红色背景但没有动画
**原因**: lottie 动画数据有问题
**解决**: 检查 AnimatedLottieView 组件是否正确导入

## 下一步调试

如果以上步骤都正常，但仍然看不到动画，请告诉我：

1. 是否看到红色背景？
2. 控制台是否有相关日志？
3. 状态显示是否正常？
4. 点击测试按钮是否有反应？

这样我可以进一步定位问题所在。
