# 听书任务引导动画简单实现

## 实现方式

### 1. 自动显示
- 当听书任务模块成功加载时（`listenTaskInfo.success` 为 true）
- 延迟1秒后自动显示引导动画
- 3秒后自动隐藏

### 2. 简化动画
- 使用简单的红色圆形背景替代复杂的lottie动画
- 显示"点击"文字提示
- 位置：听书任务按钮右侧

### 3. 调试功能
- 开发模式下显示调试信息
- 右上角显示引导状态
- 控制台输出相关日志

## 测试方法

1. **进入福利中心页面**
2. **找到听书任务模块**
3. **观察是否在按钮右侧显示红色圆形引导**
4. **查看右上角调试信息**
5. **检查控制台日志**

## 预期效果

- 听书任务模块加载后1秒，按钮右侧出现红色圆形引导
- 引导显示"点击"文字
- 3秒后自动消失
- 开发模式下右上角显示"引导: 显示"或"引导: 隐藏"

## 如果看不到引导

1. 检查是否在开发模式
2. 查看右上角调试信息
3. 检查控制台是否有"自动显示引导动画"日志
4. 确认听书任务模块是否正常加载

## 后续优化

如果这个简单实现能正常工作，我们可以：
1. 替换为更美观的动画效果
2. 添加手势引导图标
3. 优化显示时机和持续时间
4. 添加用户交互反馈
