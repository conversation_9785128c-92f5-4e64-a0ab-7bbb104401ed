# 动画显示问题调试

## 问题描述

从控制台日志可以看到：
```
显示引导动画，状态: {"showGuide": false, "success": true}
```

`showGuide` 一直是 `false`，说明 `setShowListenTaskGuide(true)` 没有生效。

## 调试措施

### 1. 添加状态变化监听
```typescript
// 监听showListenTaskGuide状态变化
useEffect(() => {
  console.log('showListenTaskGuide状态变化:', showListenTaskGuide)
}, [showListenTaskGuide])
```

### 2. 添加条件检查日志
```typescript
console.log('检查动画显示条件:', { 
  success: listenTaskInfo.success, 
  showGuide: showListenTaskGuide,
  shouldShow: listenTaskInfo.success && !showListenTaskGuide 
})
```

### 3. 添加设置状态日志
```typescript
console.log('设置动画显示为true')
setShowListenTaskGuide(true)
```

## 可能的问题原因

### 1. 状态更新被覆盖
- 可能有其他代码在同时修改 `showListenTaskGuide` 状态
- 可能存在竞态条件

### 2. 组件重新渲染
- 组件可能在状态设置后立即重新渲染
- 状态可能被重置

### 3. 依赖项问题
- `useEffect` 的依赖项可能导致无限循环
- 状态更新可能触发不必要的重新渲染

## 调试步骤

1. **观察状态变化日志**
   - 查看 `showListenTaskGuide状态变化` 日志
   - 确认状态是否真的被设置为 `true`

2. **观察条件检查日志**
   - 查看 `检查动画显示条件` 日志
   - 确认条件判断是否正确

3. **观察设置状态日志**
   - 查看 `设置动画显示为true` 日志
   - 确认 `setShowListenTaskGuide(true)` 是否被调用

## 预期结果

如果调试正常，应该看到：
```
检查动画显示条件: {"success": true, "showGuide": false, "shouldShow": true}
设置动画显示为true
showListenTaskGuide状态变化: true
```

## 下一步行动

根据调试日志结果：
- 如果状态没有变化，检查是否有其他代码在覆盖状态
- 如果状态有变化但动画不显示，检查渲染条件
- 如果条件检查有问题，修复逻辑判断

## 文件修改

**文件**: `src/components/CoinCenter/ListenTask/index.tsx`

**修改内容**:
- 添加状态变化监听
- 添加条件检查日志
- 添加设置状态日志
