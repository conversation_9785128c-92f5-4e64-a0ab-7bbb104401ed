# 听书任务引导动画功能说明

## 功能概述

实现了新人礼包模块的听书任务点击按钮定位到真实的听书任务模块，并在听书任务模块的按钮位置增加手势引导动画。

## 实现原理

### 1. 事件驱动架构
- 使用 `GlobalEventEmitter` 实现模块间通信
- 新人礼包模块触发定位事件
- 听书任务模块监听并响应事件

### 2. 定位机制
- 新人礼包点击听书任务时触发 `locateListenTask` 事件
- 福利中心页面监听该事件并滚动到听书任务模块
- 听书任务模块测量自身位置并触发 `scrollToListenTask` 事件

### 3. 引导动画
- 参考大转盘的 lottie 动画实现
- 使用 `AnimatedLottieView` 组件播放手势引导动画
- 动画位置基于按钮的实际位置计算

## 核心代码

### 新人礼包模块 (NewbieGift)
```typescript
const handleListenTask = (task: any) => {
  try {
    setShowListenTaskGuide(false)
    Toast.info('正在定位到听书模块...')
    GlobalEventEmitter.emit('locateListenTask')
    GlobalEventEmitter.emit('showListenTaskGuide')
  } catch (error) {
    console.error('定位听书模块失败:', error)
    Toast.info('定位失败，请稍后重试')
  }
}
```

### 听书任务模块 (ListenTask)
```typescript
// 监听定位事件
useEffect(() => {
  const locateListener = GlobalEventEmitter.addListener('locateListenTask', () => {
    if (listenTaskRef.current) {
      listenTaskRef.current.measure((x, y, width, height, pageX, pageY) => {
        GlobalEventEmitter.emit('scrollToListenTask', { y: pageY });
      });
    }
  });
  return () => locateListener?.remove();
}, []);

// 监听显示引导动画事件
useEffect(() => {
  const showGuideListener = GlobalEventEmitter.addListener('showListenTaskGuide', () => {
    setTimeout(() => {
      if (buttonRef.current) {
        buttonRef.current.measure((x, y, width, height, pageX, pageY) => {
          setGuideRect({ x: pageX, y: pageY, width, height })
          setShowListenTaskGuide(true)
          setTimeout(() => setShowListenTaskGuide(false), 3000)
        })
      }
    }, 500)
  });
  return () => showGuideListener?.remove();
}, []);
```

### 福利中心页面 (CoinCenter)
```typescript
// 监听滚动到听书模块事件
const scrollToListenTaskListener = GlobalEventEmitter.addListener('scrollToListenTask', (data: { y: number }) => {
  if (scrollViewRef.current && data.y) {
    const safeDistance = px(200);
    const targetY = Math.max(0, data.y - headerHeight - safeDistance);
    scrollViewRef.current.scrollTo({ y: targetY, animated: true });
  }
});
```

## 使用方法

### 1. 正常使用流程
1. 用户在新人礼包模块点击听书任务
2. 系统自动滚动到听书任务模块
3. 显示手势引导动画，提示用户点击按钮
4. 3秒后自动隐藏引导动画

### 2. 开发测试
在开发模式下，听书任务模块右上角会显示一个测试按钮，可以手动触发/隐藏引导动画。

## 技术要点

### 1. 位置计算
- 使用 `measure` 方法获取组件的实际位置
- 考虑页面头部高度和安全距离
- 确保动画位置准确

### 2. 动画实现
- 使用 lottie 动画文件
- 动画大小：54x54 像素
- 自动播放和循环

### 3. 事件清理
- 在组件卸载时清理事件监听器
- 避免内存泄漏

## 注意事项

1. 确保所有相关模块都正确引入了 `GlobalEventEmitter`
2. 动画位置计算需要考虑不同屏幕尺寸的适配
3. 在组件卸载时及时清理事件监听器
4. 引导动画仅在开发模式下显示测试按钮

## 扩展功能

该架构可以轻松扩展到其他任务模块：
- 翻卡任务：`locateFlipCardTask`
- 大转盘任务：`locateSpinningWheel`
- 其他自定义任务模块
