import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from 'servicesV2/request';
import userInfoDetail from 'modulesV2/userInfoDetail';

export enum NewUserTaskStatus {
  COMPLETED = 0,    // 所有任务已完成，需要置灰
  INITIAL = 1,      // 任务最开始的未完成状态
  CLAIMABLE_2 = 2,  // 可完成任务状态2
  CLAIMABLE_3 = 3,  // 可完成任务状态3  
  CLAIMABLE_4 = 4,  // 可完成任务状态4
  // 可以继续扩展更多状态...
}

export interface NewUserTaskItem {
  type: string;
  title: string;
  subTitle: string;
  coins: number;
  extMap: string;
  btnText: string;
  btnLink: string;
  status: number; // 服务端返回数字：0=已完成，1=未完成，2/3/4...=可完成状态
  taskId: string;
}

export interface NewUserTaskInfo {
  title: string;
  subtitle: string;
  icon: string;
  list: NewUserTaskItem[];
  success: boolean;
  code: number;
  msg: string | null;
}

export interface NewUserTaskResponse {
  responseId: number;
  ret: number;
  data: NewUserTaskInfo;
}

/**
 * 获取新人任务信息
 * @returns Promise<ResDataType<NewUserTaskInfo>>
 */
export const queryNewUserTaskInfo = async (): Promise<ResDataType<NewUserTaskInfo> | undefined> => {
  const uid = userInfoDetail.getDetail().uid || -1;
  return request<NewUserTaskInfo>({
    ...API_ADSE,
    url: `ting/welfare/newUserTaskInfo/ts-${Date.now()}?uid=${uid}`,
  });
};
