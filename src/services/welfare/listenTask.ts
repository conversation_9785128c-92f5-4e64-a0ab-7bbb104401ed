import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from 'servicesV2/request';
import CryptoJS from 'crypto-js';

// export type TaskStatus = 0 | 1 | 2; // 0: 未完成, 1: 待领取, 2: 已领取

export enum TaskStatus {
  UNFINISHED = 0, // 未完成
  PENDING = 1,  // 待领取
  CLAIMED = 2  // 已领取
} 

export interface ListenTaskInfo {
  success: boolean,
  currentStep: number;
  stepInfo: {
    condition: string;
    amount: number;
    status: TaskStatus;
  }[];
  totalCoins: number;
  listenDuration: number; // 当前已听时长（秒）
  nextRewardTime: number; // 下一个奖励所需时长（秒）
  title: string;
  btnText: string;
  status: TaskStatus; // 添加任务整体状态
}

// AES 加密函数
export const encryptLocalDuration = (duration: number): string => {
  try {
    // 使用提供的密钥
    const key = CryptoJS.enc.Base64.parse('1tyt1zuKMloXu/prwDTm5Q==');
    // 将数字转换为字符串进行加密
    const plaintext = duration.toString();
    // 进行 AES 加密 - 使用 ECB 模式，PKCS7 填充 (对应 JAVA 的 AES/ECB/PKCS5Padding)
    const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
      mode: CryptoJS.mode.ECB,  // ECB 模式
      padding: CryptoJS.pad.Pkcs7,  // PKCS7 填充 (等同于 PKCS5)
    });

    // 返回 Base64 字符串
    return encrypted.toString();;
  } catch (error) {
    console.error('AES 加密失败:', error);
    return '';
  }
};

export const queryListenTaskInfo = async (localDuration?: number): Promise<ResDataType<ListenTaskInfo> | undefined> => {
  let url = `incentive/ting/welfare/queryListenTaskInfo/ts-${Date.now()}`;

  // 如果提供了 localDuration，则加密并添加到 URL 参数中
  if (localDuration !== undefined) {
    console.log('🔐 [API] 原始 localDuration:', localDuration);
    console.log('🔐 [API] localDuration 类型:', typeof localDuration);
    const encryptedDuration = encryptLocalDuration(localDuration);
    console.log('🔐 [API] 加密后的 localDuration:', encryptedDuration);

    if (encryptedDuration) {
      url += `?localDuration=${encodeURIComponent(encryptedDuration)}`;
      console.log('🔐 最终请求 URL:', url);
    }
  }

  return request<ListenTaskInfo>({
      ...API_ADSE,
      url,
    });
};
