export enum AD_SOURCE {
  AD_SIGN_IN = 'AD_SIGN_IN', // 看广告签到
  SIGN_IN_LOTTERY = 'SIGN_IN_LOTTERY', // 签到抽奖
  AD_VIDEO = 'VIDEO_TASK', // 看视频
  DAILY = 'DAILY_TASK', // 日常任务
  EXCHANGE = 'EXCHANGE', // 【换量任务功能】换量任务
  RED_PACKET_RAIN = 'RED_PACKET_RAIN', // 每日任务-红包雨
  MARKET = 'MARKET', // 每日任务-逛商场
  FLIP = 'FLIP_CARD_TASK', // 翻卡牌任务
  SIGN_IN = 'SIGN_IN', // 签到
  VALUABLE = 'HIGH_PRICE_TASK', // 惊喜任务（高客单价）
  LISTEN_TASK = 'LISTEN_TASK', // 听书任务奖励
  SUSPEND_TOUCH = 'SUSPEND_TOUCH', // TOUCH
  AUTO_SIGN_IN = 'AUTO_SIGN_IN', // 自动签到弹窗
  RANDOM_AD_REWARD = 'RANDOM_AD_REWARD', // 随机广告奖励
  SIGN_IN_WAKE_UP = 'SIGN_IN_WAKE_UP', // 签到唤端广告奖励
  SHARE_COINS = 'SHARE_COINS', // 分享得金币
  TREASURE_BOX = 'TREASURE_BOX', // 宝箱
  PLAY_LET = 'PLAY_LET', // 假设 PLAY_LET 映射到 DAILY
  INCENTIVE_TASK = 'INCENTIVE_TASK', // 唤端
  DRINK_WATER_NORMAL = 'DRINK_WATER_NORMAL', // 喝水正常打卡
  DRINK_WATER_AGAIN = 'DRINK_WATER_AGAIN', // 喝水补打卡
  DRINK_WATER_WEEKLY_REWARD = 'DRINK_WATER_WEEK', // 喝水7日连续打卡奖励
  DRINK_WATER_FULL_WEEKLY_REWARD = 'DRINK_WATER_FULL_WEEK', // 喝水7日连续满勤奖励
  DRINK_WATER_DOUBLE_REWARD = "DRINK_WATER_DOUBLE_REWARD", // 喝水打卡翻倍奖励
  SPINNING_WHEEL = 'SPINNING_WHEEL', // 九宫格抽奖
  NEWCOMER_GIFT = 'NEWCOMER_GIFT', // 新人礼包
}

export enum RewardType {
  SIGN_IN = 1, // 签到
  AD_SIGN_IN = 2, // 看广告签到
  LAUNCH = 3, // 唤端
  VALUABLE = 4, // 惊喜任务（高客单价）
  AD_VIDEO = 5, // 看视频
  DAILY = 6, // 日常任务
  FLIP = 7, // 翻卡牌任务
  SIGN_IN_LOTTERY = 8, // 签到抽奖
  TREASURE_BOX = 9, // 宝箱
  RED_PACKET_RAIN = 10, // 红包雨
  AD_RED_PACKET_RAIN = 11, // 看广告领红包雨
  LISTEN_TASK = 12,   // 听书任务奖励
  EXTRA_REWARDS = 13,  // 领取额外奖励
  SHARE_COINS = 14,  // 分享得金币
  RECEIVE_SHARE_COINS = 15, // 领取瓜分大奖
  DRINK_WATER_NORMAL = 16, // 喝水正常打卡
  DRINK_WATER_AGAIN = 17, // 喝水补打卡
  DRINK_WATER_WEEKLY_REWARD = 18, // 喝水7日连续打卡奖励
  DRINK_WATER_FULL_WEEKLY_REWARD = 19, // 喝水7日连续满勤奖励
  DRINK_WATER_DOUBLE_REWARD = 20, // 喝水打卡翻倍奖励
  SPINNING_WHEEL = 21,  //大转盘抽奖
  EXCHANGE = 22, // 【换量任务功能】换量任务
  NEWCOMER_GIFT = 25 // 新人礼包
}

export enum NonCoinRewardType {
  SHARE_COINS = 1,
  SPINNING_WHEEL = 2
}

export const AD_POSITION = {
  slotId: 307,
  positionName: 'incentive_welfare',
}

export const LISTEN_TASK_POSITION = {
  slotId: 307,
  positionName: 'incentive_welfare',
}


export enum FallbackReqType {
  NORMAL = 0,  // 正常曝光完成任务发放奖励  
  FALLBACK = 1 // 未正常曝光，异常兜底发放的奖励
}

export enum TransactionType {
  INCOME = 1, // 收入
  EXPENSE = 2, // 支出
  WITHDRAW = 3, // 提现
}

// DailyTaskType 与 AD_SOURCE 的映射
export const DAILY_TASK_TYPE_TO_AD_SOURCE: Record<string, AD_SOURCE> = {
  RED_PACKET_RAIN: AD_SOURCE.RED_PACKET_RAIN,
  TREASURE_BOX: AD_SOURCE.TREASURE_BOX, // 需要在 AD_SOURCE 中补充 TREASURE_BOX
  PLAY_LET: AD_SOURCE.PLAY_LET, // 假设 PLAY_LET 映射到 PLAY_LET
  MARKET: AD_SOURCE.MARKET,
  EXCHANGE: AD_SOURCE.EXCHANGE, // 【换量任务功能】换量任务映射
};

// DailyTaskType 与 RewardType 的映射
export const DAILY_TASK_TYPE_TO_REWARD_TYPE: Record<string, RewardType> = {
  RED_PACKET_RAIN: RewardType.RED_PACKET_RAIN,
  TREASURE_BOX: RewardType.TREASURE_BOX,
  PLAY_LET: RewardType.DAILY, // PLAY_LET 使用日常任务类型
  MARKET: RewardType.DAILY, // 商场任务使用日常任务类型
  EXCHANGE: RewardType.EXCHANGE, // 【换量任务功能】换量任务使用专用类型
  DRINK_WATER: RewardType.DRINK_WATER_NORMAL, // 喝水任务映射
};