import { atom } from 'jotai';
import { queryCommonConfig, queryRandomPopData } from 'services/welfare';

interface CommonConfigInfo {
    goldClearRule: string;   // 金币清零规则
    randomPopConfigData: {  // 随机弹窗配置
        enable: boolean;
        remainSeconds: number;
        intervalSeconds: number;
        totalTimes: number;
        text: string;
    };
    taskKeys: string[];
}

interface RandomPopDataInfo {
    success: boolean;
    valid: boolean;
    rewardGoldCoins: number;
    code: number;
    msg: string;
}

export const defaultCommonConfig: CommonConfigInfo = {
  goldClearRule: '连续30天没来，金币会清空哦~',
  randomPopConfigData: {
    enable: false,
    remainSeconds: 0,
    intervalSeconds: 0,
    totalTimes: 0,
    text: '',
  },
  taskKeys: [
    "ShareCoins",
    "ValuableTask",
    "ListenTask",
    "VideoTask",
    "FlipCard",
    "DailyTask"
  ],
};

export const commonConfigAtom = atom<CommonConfigInfo>(defaultCommonConfig);

export const writeCommonConfigAtom = atom(
  null,
  async (get, set, srcChannel: string) => {
    console.log('🔍 [CommonConfig] 开始获取通用配置:', { srcChannel });
    let config = defaultCommonConfig;
    console.log('🔍 [CommonConfig] 默认配置:', config);

    try {
      const commonConfig = await queryCommonConfig(srcChannel);
      console.log('🔍 [CommonConfig] API 返回结果:', commonConfig);

      if (
        commonConfig?.ret === 0 &&
        commonConfig.data.success &&
        Array.isArray(commonConfig.data.taskKeys) &&
        commonConfig.data.taskKeys.length > 0
      ) {
        config = commonConfig.data;
        console.log('🔍 [CommonConfig] 使用 API 配置:', config);
      } else {
        console.log('🔍 [CommonConfig] API 配置无效，使用默认配置');
      }
    } catch (e) {
      console.error('🔍 [CommonConfig] 获取配置失败，使用默认配置:', e);
    }

    console.log('🔍 [CommonConfig] 最终配置:', config);
    set(commonConfigAtom, config);
  }
);

export const randomPopDataAtom = atom<RandomPopDataInfo | null>(null);

export const writeRandomPopDataAtom = atom(
  null,
  async (get, set) => {
    const randomPopData = await queryRandomPopData();
    if (randomPopData?.ret === 0 && randomPopData.data.success) {
        set(randomPopDataAtom, randomPopData.data);
    }
  },
);

