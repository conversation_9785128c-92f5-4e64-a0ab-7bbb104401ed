import React, { useEffect, useRef, useState, useContext } from "react";
import { Animated, View, Platform, NativeModules } from "react-native"
import { useAtomValue, useAtom, useSetAtom } from "jotai";
import { getStyles } from "./style";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { isAndroid } from "@xmly/rn-utils";
// import nativeInfoModule from '../../modulesV2/nativeInfoModule'
import Balance from "components/CoinCenter/Balance";
import SignIn from "components/CoinCenter/SignIn";
// import AdPopWindow from "components/CoinCenter/AdPopWindow";
import PopWindow from "components/CoinCenter/DailyTask/PopWindow";
import AdPopWindowForVideo from "components/CoinCenter/AdPopWindowForVideo";
import ValuableTask from "components/CoinCenter/ValuableTask";
import VideoTask from "components/CoinCenter/VideoTask";
import FlipCard from "components/CoinCenter/FlipCardTask";
import DailyTask from "components/CoinCenter/DailyTask";
import ListenTask from "components/CoinCenter/ListenTask";
import NewbieGift from "components/CoinCenter/NewbieGift";
import ShareCoins from 'components/CoinCenter/ShareCoins'
// import ShareCoinsModal from 'components/CoinCenter/ShareCoinsModal'
import ShareCoinsCollapse from 'components/CoinCenter/ShareCoins/ShareCoinsCollapse'
// import ShareCoinsRewardModal from 'components/CoinCenter/ShareCoins/ShareCoinsRewardModal'
import AnnouncementBanner from "components/CoinCenter/AnnouncementBanner";
import ModalManager from 'components/common/ModalManager';
import CheckInCollapse from "components/CoinCenter/CheckInCollapse";
import ListenTaskModal from 'components/CoinCenter/ListenTask/ListenTaskModal'
// import SpinningWheelModal from 'components/CoinCenter/SpiningWheel/SpinningWheelModal'
import SpiningWheel from "components/CoinCenter/SpiningWheel";
import coinCenterThemeAtom from './theme';
import { BetterImage } from "@xmly/rn-components";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProps } from "router/type";
import { scrollValueAtom } from './store/scroll';
import Header from "components/CoinCenter/Header";
import { isHarmony } from "../../../rnEnv";
import { NativeInfoContext } from 'contextV2/nativeInfoContext'
import Rule from 'components/CoinCenter/Rule';
import { usePageReport } from "hooks/usePageReport";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "router/type";
// import { ScrollAnalyticComp } from "@xmly/react-native-page-analytics";
import { updateWelfareAtom } from "atom/welfare";
import { PageEventEmitter } from "defs";
import RedPacketRain from 'components/CoinCenter/RedPacketRain';
import { showRedPacketRainAtom } from 'atom/redPacketRain';
// import RedPacketRain from 'components/CoinCenter/RedPacketRain/demo';
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
// import NotificationPopup from 'components/CoinCenter/NotificationPopup';
import TouchTask from "components/CoinCenter/TouchTask";
import { ScrollEventName } from "./constants";
import { writeCommonConfigAtom } from "./store/commonConfig";
import { commonConfigAtom } from "./store/commonConfig";
import { px } from 'utils/px';

// 全局标记，用于记录是否已经执行过自动跳转（放在组件外部，避免组件重建时重置）
let hasAutoNavigatedToDrinkWater = false;



const componentMap = {
  ShareCoins,
  ValuableTask,
  ListenTask,
  NewbieGift,
  VideoTask,
  FlipCard,
  DailyTask,
  SpiningWheel
};

export default function CoinCenter(props: StackScreenProps<RootStackParamList>) {
  const theme = useAtomValue(coinCenterThemeAtom);
  const styles = getStyles(theme);
  const navigation = useNavigation<RootNavigationProps>()
  const [scrollValue] = useAtom(scrollValueAtom);
  // 计算Header的初始预估高度，避免初始化时重叠
  const safeArea = useSafeAreaInsets();
  const estimatedHeaderHeight = isAndroid ? safeArea.top + 50 : 44 + safeArea.top;
  const [headerHeight, setHeaderHeight] = useState(estimatedHeaderHeight);
  const nativeInfo = useContext(NativeInfoContext);
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const showRedPacketRain = useAtomValue(showRedPacketRainAtom);

  const commonConfig = useAtomValue(commonConfigAtom);
  const setCommonConfig = useSetAtom(commonConfigAtom);
  const queryCommonConfig = useSetAtom(writeCommonConfigAtom);

  const [showSignModal, setShowSignModal] = useState(true);
  const [popVisible, setPopVisible] = useState(false);
  const [rewardCoin, setRewardCoin] =useState<number>(0);
  const VideoModalRef = useRef(null);
  const VideoRef = useRef(null);
  const scrollViewRef = useRef<any>(null);

  usePageReport({
    pageViewCode: 67685,
    pageExitCode: 67686,
    currPage: 'welfareCenter',
    params: {
      from: nativeInfo?.srcChannel ?? 'unknown',
      sourceType: nativeInfo?.srcChannel ?? 'unknown'
    },
    otherProps: props
  });

  const handleScroll = Animated.event([{ nativeEvent: { contentOffset: { y: scrollValue } } }], {
    useNativeDriver: true,
    listener: (event: any) => {
      // ScrollEventSender.send(coinCenterId, 'scroll')
      GlobalEventEmitter.emit(ScrollEventName.onScroll);
    }
  });

  useEffect(() => {
    // 去掉骨架屏
    GlobalEventEmitter.emit('appContentReady');

    // iOS设备：如果是通过 route=drinkWater 进入的，立即自动跳转到喝水页面
    if (Platform.OS === 'ios' && nativeInfo?.route === 'drinkWater' && !hasAutoNavigatedToDrinkWater) {
      hasAutoNavigatedToDrinkWater = true;
      // 使用requestAnimationFrame确保在下一帧执行导航
      requestAnimationFrame(() => {
        navigation.push('DrinkWater'); // 使用push而不是navigate，确保导航栈的正确性
      });
    }

    navigation.addListener('blur', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
      }
    });

    navigation.addListener('focus', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(true)
      }
    });

    const resumeListener = PageEventEmitter.addListener('onResume', () => {
      console.log("onResuming....................")
      updateWelfare();
    });

    // 监听听书模块定位事件
    const locateListenTaskListener = GlobalEventEmitter.addListener('locateListenTask', () => {
      // 这里需要计算听书模块的位置并滚动到该位置
      // 暂时使用延迟滚动，确保组件已经渲染完成
      setTimeout(() => {
        if (scrollViewRef.current) {
          // 计算听书模块的大概位置（需要根据实际布局调整）
          const estimatedListenTaskPosition = 800; // 预估位置，需要根据实际布局调整
          scrollViewRef.current.scrollTo({ y: estimatedListenTaskPosition, animated: true });
        }
      }, 100);
    });

    // 监听滚动到听书模块事件
    const scrollToListenTaskListener = GlobalEventEmitter.addListener('scrollToListenTask', (data: { y: number }) => {
      if (scrollViewRef.current && data.y) {
        // 基于听书任务组件的实际位置进行精确计算
        // 减去header高度，并添加足够的安全距离
        const safeDistance = px(200); // 使用px函数确保一致性，增加安全距离
        const targetY = Math.max(0, data.y - headerHeight - safeDistance);
        console.log('滚动到听书模块:', {
          originalY: data.y,
          headerHeight,
          safeDistance,
          targetY
        });
        scrollViewRef.current.scrollTo({ y: targetY, animated: true });
      }
    });

    // 监听滚动到翻卡任务模块事件
    const scrollToFlipCardTaskListener = GlobalEventEmitter.addListener('scrollToFlipCardTask', (data: { y: number }) => {
      if (scrollViewRef.current && data.y) {
        // 基于翻卡任务组件的实际位置进行精确计算
        // 减去header高度，并添加足够的安全距离
        const safeDistance = px(200); // 使用px函数确保一致性，增加安全距离
        const targetY = Math.max(0, data.y - headerHeight - safeDistance);
        scrollViewRef.current.scrollTo({ y: targetY, animated: true });
      }
    });

    // 监听滚动到大转盘任务模块事件
    const scrollToSpinningWheelListener = GlobalEventEmitter.addListener('scrollToSpinningWheel', (data: { y: number }) => {
      if (scrollViewRef.current && data.y) {
        // 基于大转盘任务组件的实际位置进行精确计算
        // 减去header高度，并添加足够的安全距离
        const safeDistance = px(200); // 使用px函数确保一致性，增加安全距离
        const targetY = Math.max(0, data.y - headerHeight - safeDistance);
        scrollViewRef.current.scrollTo({ y: targetY, animated: true });
      }
    });



    return () => {
      resumeListener?.remove();
      locateListenTaskListener?.remove();
      scrollToListenTaskListener?.remove();
      scrollToFlipCardTaskListener?.remove();
      scrollToSpinningWheelListener?.remove();
    }
  }, []);

  useEffect(() => {
    // 1. 先重置为 loading: true
    setCommonConfig({ ...commonConfig, loading: true });
    // 2. 再查询配置
    queryCommonConfig(nativeInfo?.srcChannel ?? 'unknown');

    return () => {
      setCommonConfig(prev => ({ ...prev, loading: true }));
    }
  }, []);

  function navigateToHome() {
    GlobalEventEmitter.emit('appContentLoading')
    navigation.replace('Home')
  }

  function handleScrollBeginDrag() {
    GlobalEventEmitter.emit(ScrollEventName.onScrollBeginDrag);
  }

  function handleScrollEndDrag() {
    GlobalEventEmitter.emit(ScrollEventName.onScrollEndDrag);
  }

  function handleMomentumScrollBegin() {
    GlobalEventEmitter.emit(ScrollEventName.onMomentumScrollBegin);
  }

  function handleMomentumScrollEnd() {
    GlobalEventEmitter.emit(ScrollEventName.onMomentumScrollEnd);
  }
  const onRewardCoinFinish = (coin:number)=>{
    setRewardCoin(coin);
    setPopVisible(true);
  }
  const onFinishVideo = (cadId?: number | undefined, adResponseId?: number, rewardCoin?: number, fallbackReq?:number,ecpm?: string) => {
    (VideoModalRef?.current as any)?.finishVideo(cadId, adResponseId, rewardCoin,fallbackReq,ecpm);
  }

  const onNextVideo = () => {
    (VideoRef?.current as any)?.nextVideo();
  }

  const onFinishVidoeReward= () => {
    (VideoRef?.current as any)?.finishVidoeReward();
  }


  return (
    <View style={[styles.container]}>
      <Header
        onTabPress={navigateToHome}
        onLayout={(actualHeight) => {
          // 只有当实际高度与当前高度差异较大时才更新，避免不必要的重渲染
          //console.log('>>height: actualHeight= ' + actualHeight + ' ' + headerHeight)
          if (Math.abs(actualHeight - headerHeight) > 3) {
            setHeaderHeight(actualHeight);
          }
        }}
        singleTab={isHarmony}
        hideTitle={nativeInfo?.embed}
      />
      <View style={{ flex: 1 }}>
        <Animated.ScrollView
          ref={scrollViewRef}
          style={[styles.scrollView]}
          contentContainerStyle={styles.scrollContent}
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onScrollEndDrag={handleScrollEndDrag}
          onMomentumScrollBegin={handleMomentumScrollBegin}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <BetterImage
            source={{
              uri: theme.headerBg
            }}
            style={styles.headerBg}
          />
          <View style={{ height: headerHeight }}></View>
          <AnnouncementBanner />

          <View style={styles.content}>
            <Balance showSignModal={showSignModal} />
            {showSignModal? (<BetterImage
              source={{
                uri: theme.splitLine
              }}
              imgWidth={343}
              imgHeight={9}
              style={styles.splitLine}
            />) : null}
            <NewbieGift />
            <SignIn setShowSignModal={setShowSignModal} />
          </View>
          <View>
            {
              commonConfig.taskKeys
                .filter(key => key !== 'NewbieGift') // 过滤掉NewbieGift，避免重复渲染
                .map(key => {
                  const Comp = componentMap[key as keyof typeof componentMap];
                  if (!Comp) {
                    return null;
                  }

                  // 为 VideoTask 组件添加 ref
                  if (key === 'VideoTask') {
                    return <Comp ref={VideoRef} onFinishVideo={onFinishVideo} key={key} onRewardCoinFinish={onRewardCoinFinish}/>;
                  }

                  return <Comp onFinishVideo={onFinishVideo} key={key} onRewardCoinFinish={onRewardCoinFinish}/>;
                })
            }
          </View>
          <CheckInCollapse />
          <ShareCoinsCollapse />
          <Rule />
        </Animated.ScrollView>
      </View>

      <ModalManager />

      <PopWindow rewardCoin={rewardCoin} popVisible={popVisible} onClose={() => setPopVisible(false)}/>
      <AdPopWindowForVideo onFinishVidoeReward={onFinishVidoeReward} onNextVideo={onNextVideo} ref={VideoModalRef} />
      <ListenTaskModal visible={true} onClose={() => {}} type="withAd" />
      {/* <SpinningWheelModal visible={false} onClose={() => {}} /> */}
      <RedPacketRain visible={showRedPacketRain} />
      <TouchTask />
    </View>
  )
}