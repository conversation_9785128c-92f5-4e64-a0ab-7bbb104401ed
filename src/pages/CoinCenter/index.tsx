import React, { useEffect,  useRef, useCallback, useState, useContext } from "react";
import { Animated, View, Platform, NativeModules } from "react-native"
import { useAtomValue, useAtom, useSetAtom } from "jotai";
import { getStyles } from "./style";
import nativeInfoModule from '../../modulesV2/nativeInfoModule'
import Balance from "components/CoinCenter/Balance";
import SignIn from "components/CoinCenter/SignIn";
import AdPopWindow from "components/CoinCenter/AdPopWindow";
import PopWindow from "components/CoinCenter/DailyTask/PopWindow";
import AdPopWindowForVideo from "components/CoinCenter/AdPopWindowForVideo";
import ValuableTask from "components/CoinCenter/ValuableTask";
import { VideoTask } from "components/CoinCenter/VideoTask";
import FlipCard from "components/CoinCenter/FlipCardTask";
import DailyTask from "components/CoinCenter/DailyTask";
import ListenTask from "components/CoinCenter/ListenTask";
import ShareCoins from 'components/CoinCenter/ShareCoins'
import ShareCoinsModal from 'components/CoinCenter/ShareCoinsModal'
import ShareCoinsCollapse from 'components/CoinCenter/ShareCoins/ShareCoinsCollapse'
import AnnouncementBanner from "components/CoinCenter/AnnouncementBanner";
import CheckInCollapse from "components/CoinCenter/CheckInCollapse";
import ListenTaskModal from 'components/CoinCenter/ListenTask/ListenTaskModal'
import coinCenterThemeAtom from './theme';
import { BetterImage } from "@xmly/rn-components";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProps } from "router/type";
import { scrollValueAtom } from './store/scroll';
import Header from "components/CoinCenter/Header";
import { isHarmony } from "../../../rnEnv";
import { NativeInfoContext } from 'contextV2/nativeInfoContext'
import Rule from 'components/CoinCenter/Rule';
import { usePageReport } from "hooks/usePageReport";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "router/type";
import { ScrollAnalyticWapper, ScrollEventSender } from "@xmly/react-native-page-analytics";
import { updateWelfareAtom } from "atom/welfare";
import { PageEventEmitter } from "defs";
import RedPacketRain from 'components/CoinCenter/RedPacketRain';
import { showRedPacketRainAtom } from 'atom/redPacketRain';
// import RedPacketRain from 'components/CoinCenter/RedPacketRain/demo';
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
// import NotificationPopup from 'components/CoinCenter/NotificationPopup';
import TouchTask from "components/CoinCenter/TouchTask";
import { ScrollEventName } from "./constants";
import { writeCommonConfigAtom } from "./store/commonConfig";
import { commonConfigAtom } from "./store/commonConfig";
import { getTouchToWelfareEnableABTest } from "utilsV2/getTouchToWelfareEnableABTest";

const coinCenterId = 'coinCenterContent';

const componentMap = {
  ShareCoins,
  ValuableTask,
  ListenTask,
  VideoTask,
  FlipCard,
  DailyTask
};

export default function CoinCenter(props: StackScreenProps<RootStackParamList>) {
  const theme = useAtomValue(coinCenterThemeAtom);
  const styles = getStyles(theme);
  const navigation = useNavigation<RootNavigationProps>()
  const [scrollValue] = useAtom(scrollValueAtom);
  const [headerHeight, setHeaderHeight] = useState(0);
  const nativeInfo = useContext(NativeInfoContext);
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const showRedPacketRain = useAtomValue(showRedPacketRainAtom);

  const commonConfig = useAtomValue(commonConfigAtom);
  const queryCommonConfig = useSetAtom(writeCommonConfigAtom);

  // 监听 commonConfig 变化
  useEffect(() => {
    console.log('🔍 [CoinCenter] commonConfig 变化:', commonConfig);
  }, [commonConfig]);

  const [adHeight, setAdHeight] = useState<number | undefined>(undefined);
  const [showSignModal, setShowSignModal] = useState(true);
  const [popVisible, setPopVisible] = useState(false);
  const [rewardCoin, setRewardCoin] =useState<number>(0);
  const VideoModalRef = useRef(null);
  const VideoRef = useRef(null);

  const [hideModal, setHideModal] = useState(false);

  // 控制 ListenTask 模块的显示
  const [shouldShowListenTask, _setShouldShowListenTask] = useState(() => {
    console.log('🔍 [CoinCenter] shouldShowListenTask 初始化为 false');
    return false;
  });

  // 包装 setter 来追踪所有状态变更
  const setShouldShowListenTask = (value: boolean | ((prev: boolean) => boolean)) => {
    const actualValue = typeof value === 'function' ? value(shouldShowListenTask) : value;
    console.log('🔍 [CoinCenter] setShouldShowListenTask 被调用:', {
      inputValue: value,
      actualValue,
      currentValue: shouldShowListenTask,
      stackTrace: new Error().stack
    });
    _setShouldShowListenTask(actualValue);
  };

  // 监听 shouldShowListenTask 状态变化
  useEffect(() => {
    console.log('🔍 [CoinCenter] shouldShowListenTask 状态变化:', {
      newValue: shouldShowListenTask,
      type: typeof shouldShowListenTask,
      timestamp: new Date().toISOString()
    });
  }, [shouldShowListenTask]);

  usePageReport({
    pageViewCode: 67685,
    pageExitCode: 67686,
    currPage: 'welfareCenter',
    params: {
      from: nativeInfo?.srcChannel ?? 'unknown',
      sourceType: nativeInfo?.srcChannel ?? 'unknown'
    },
    otherProps: props
  });

  // 检查 AB 测试配置以决定是否显示 ListenTask
  useEffect(() => {
    const checkABTestConfig = async () => {
      console.log('🔍 [CoinCenter] 开始检查 ListenTask 显示条件');
      console.log('🔍 [CoinCenter] 当前 shouldShowListenTask 状态:', shouldShowListenTask);

      const shouldShow = await getTouchToWelfareEnableABTest();
      console.log('🔍 [CoinCenter] ListenTask 检查结果:', {
        shouldShow,
        shouldShowType: typeof shouldShow,
        willSetState: shouldShow
      });

      console.log('🔍 [CoinCenter] 即将调用 setShouldShowListenTask，值为:', shouldShow);
      setShouldShowListenTask(shouldShow);
      console.log('🔍 [CoinCenter] setShouldShowListenTask 已调用完成');

      // 延迟检查状态是否真的被设置了
      setTimeout(() => {
        console.log('🔍 [CoinCenter] 延迟检查 shouldShowListenTask 状态:', shouldShowListenTask);
      }, 100);
    };

    checkABTestConfig();
  }, []);

  const handleScroll = Animated.event([{ nativeEvent: { contentOffset: { y: scrollValue } } }], {
    useNativeDriver: true,
    listener: (event: any) => {
      ScrollEventSender.send(coinCenterId, 'scroll')
      GlobalEventEmitter.emit(ScrollEventName.onScroll);
    }
  });

  useEffect(() => {
    const nativeInfo = nativeInfoModule.getInfo()
    if(nativeInfo?.srcChannel?.includes('ListenTask')){
      setHideModal(true)
    }
  },[])

  useEffect(() => {
    // 去掉骨架屏
    GlobalEventEmitter.emit('appContentReady');

    navigation.addListener('blur', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
      }
    });

    navigation.addListener('focus', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(true)
      }
    });

    const resumeListener = PageEventEmitter.addListener('onResume', () => {
      updateWelfare();
    });

    return () => {
      resumeListener?.remove();
    }
  }, []);

  useEffect(() => {
    queryCommonConfig(nativeInfo?.srcChannel ?? 'unknown');
  }, []);


  function navigateToHome() {
    GlobalEventEmitter.emit('appContentLoading')
    navigation.replace('Home')
  }

  function handleScrollBeginDrag() {
    GlobalEventEmitter.emit(ScrollEventName.onScrollBeginDrag);
  }

  function handleScrollEndDrag() {
    GlobalEventEmitter.emit(ScrollEventName.onScrollEndDrag);
  }

  function handleMomentumScrollBegin() {
    GlobalEventEmitter.emit(ScrollEventName.onMomentumScrollBegin);
  }

  function handleMomentumScrollEnd() {
    GlobalEventEmitter.emit(ScrollEventName.onMomentumScrollEnd);
  }
  const onRewardCoinFinish = (coin:number)=>{
    setRewardCoin(coin);
    setPopVisible(true);
  }
  const onFinishVideo = (cadId?: number | undefined, adResponseId?: number, rewardCoin?: number, fallbackReq?:number) => {
    (VideoModalRef?.current as any)?.finishVideo(cadId, adResponseId, rewardCoin,fallbackReq);
  }

  const onNextVideo = () => {
    (VideoRef?.current as any)?.nextVideo();
  }

  const onFinishVidoeReward= () => {
    (VideoRef?.current as any)?.finishVidoeReward();
  }


  return (
    <View style={[styles.container]}>
      {/* <Header
        onTabPress={navigateToHome}
        onLayout={setHeaderHeight}
        singleTab={isHarmony}
        hideTitle={nativeInfo?.embed}
      /> */}
      <ScrollAnalyticWapper
        id={coinCenterId}
        viewStyle={{ flex: 1 }}
        //@ts-ignore
        useNavigation={useNavigation}
      >
        <Animated.ScrollView
          style={[styles.scrollView]}
          contentContainerStyle={styles.scrollContent}
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onScrollEndDrag={handleScrollEndDrag}
          onMomentumScrollBegin={handleMomentumScrollBegin}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <BetterImage
            source={{
              uri: theme.headerBg
            }}
            style={styles.headerBg}
          />
          <View style={{ height: headerHeight }}></View>
          <AnnouncementBanner />

          <View style={styles.content}>
            <Balance showSignModal={showSignModal} />
            {showSignModal? (<BetterImage
              source={{
                uri: theme.splitLine
              }}
              imgWidth={343}
              imgHeight={9}
              style={styles.splitLine}
            />) : null}
            <SignIn adHeight={adHeight} setShowSignModal={setShowSignModal} />
          </View>

          <View>
            {
              (() => {
                console.log('🔍 [CoinCenter] 任务过滤开始:', {
                  originalTaskKeys: commonConfig.taskKeys,
                  shouldShowListenTask,
                  componentMap: Object.keys(componentMap)
                });

                const filteredKeys = commonConfig.taskKeys
                  .filter(key => {
                    const shouldKeep = key !== 'ValuableTask' && key !== 'DailyTask' && (key !== 'ListenTask' || shouldShowListenTask);
                    console.log(`🔍 [CoinCenter] 任务 ${key}:`, {
                      isValuableTask: key === 'ValuableTask',
                      isDailyTask: key === 'DailyTask',
                      isListenTask: key === 'ListenTask',
                      shouldShowListenTask: shouldShowListenTask,
                      shouldKeep
                    });
                    return shouldKeep;
                  });

                console.log('🔍 [CoinCenter] 过滤后的任务:', filteredKeys);

                return filteredKeys.map(key => {
                  const Comp = componentMap[key as keyof typeof componentMap];
                  console.log(`🔍 [CoinCenter] 渲染任务 ${key}:`, {
                    hasComponent: !!Comp,
                    componentName: Comp?.name
                  });
                  return Comp ? <Comp  onFinishVideo={onFinishVideo} key={key} onRewardCoinFinish={onRewardCoinFinish}/> : null;
                });
              })()
            }
          </View>
          <CheckInCollapse adHeight={adHeight} />
          <ShareCoinsCollapse />
          <Rule />
        </Animated.ScrollView>
      </ScrollAnalyticWapper>
      {!hideModal ? <AdPopWindow adHeight={adHeight} setAdHeight={setAdHeight} /> : null }
      <PopWindow rewardCoin={rewardCoin} popVisible={popVisible} onClose={() => setPopVisible(false)}/>
      <AdPopWindowForVideo onFinishVidoeReward={onFinishVidoeReward} onNextVideo={onNextVideo} ref={VideoModalRef} adHeight={adHeight} setAdHeight={setAdHeight} />
      <ListenTaskModal visible={true} onClose={() => {}} type="withAd" />
      <RedPacketRain visible={showRedPacketRain} />
      <TouchTask />
      <ShareCoinsModal />

      {/* 通知弹窗 */}
      {/* {!hideModal ? <NotificationPopup /> : null } */}
    </View>
  )
}