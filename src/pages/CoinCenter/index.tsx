import React, { useEffect, useCallback, useState, useContext } from "react";
import { Animated, View, Platform, NativeModules } from "react-native"
import { useAtomValue, useAtom, useSetAtom } from "jotai";
import { getStyles } from "./style";
import Balance from "components/CoinCenter/Balance";
import SignIn from "components/CoinCenter/SignIn";
import AdPopWindow from "components/CoinCenter/AdPopWindow";
import ValuableTask from "components/CoinCenter/ValuableTask";
import VideoTask from "components/CoinCenter/VideoTask";
import FlipCard from "components/CoinCenter/FlipCardTask";
import DailyTask from "components/CoinCenter/DailyTask";
import ListenTask from "components/CoinCenter/ListenTask";
import ShareCoins from 'components/CoinCenter/ShareCoins'
import ShareCoinsCollapse from 'components/CoinCenter/ShareCoins/ShareCoinsCollapse'
import AnnouncementBanner from "components/CoinCenter/AnnouncementBanner";
import CheckInCollapse from "components/CoinCenter/CheckInCollapse";
import ListenTaskModal from 'components/CoinCenter/ListenTask/ListenTaskModal'
import coinCenterThemeAtom from './theme';
import { BetterImage } from "@xmly/rn-components";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProps } from "router/type";
import { scrollValueAtom } from './store/scroll';
import Header from "components/CoinCenter/Header";
import { isHarmony } from "../../../rnEnv";
import { NativeInfoContext } from 'contextV2/nativeInfoContext'
import Rule from 'components/CoinCenter/Rule';
import { usePageReport } from "hooks/usePageReport";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "router/type";
import { ScrollAnalyticWapper, ScrollEventSender } from "@xmly/react-native-page-analytics";
import { updateWelfareAtom } from "atom/welfare";
import { PageEventEmitter } from "defs";
import RedPacketRain from 'components/CoinCenter/RedPacketRain';
import { showRedPacketRainAtom } from 'atom/redPacketRain';
// import RedPacketRain from 'components/CoinCenter/RedPacketRain/demo';
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
import TouchTask from "components/CoinCenter/TouchTask";
import { ScrollEventName } from "./constants";

const coinCenterId = 'coinCenterContent';

export default function CoinCenter(props: StackScreenProps<RootStackParamList>) {
  const theme = useAtomValue(coinCenterThemeAtom);
  const styles = getStyles(theme);
  const navigation = useNavigation<RootNavigationProps>()
  const [scrollValue] = useAtom(scrollValueAtom);
  const [headerHeight, setHeaderHeight] = useState(0);
  const nativeInfo = useContext(NativeInfoContext);
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const showRedPacketRain = useAtomValue(showRedPacketRainAtom);

  const [adHeight, setAdHeight] = useState<number | undefined>(undefined);
  const [showSignModal, setShowSignModal] = useState(true);

  usePageReport({
    pageViewCode: 67685,
    pageExitCode: 67686,
    currPage: 'welfareCenter',
    otherProps: props
  });

  const handleScroll = Animated.event([{ nativeEvent: { contentOffset: { y: scrollValue } } }], {
    useNativeDriver: true,
    listener: (event: any) => {
      ScrollEventSender.send(coinCenterId, 'scroll')
      GlobalEventEmitter.emit(ScrollEventName.onScroll);
    }
  });

  useEffect(() => {
    // 去掉骨架屏
    GlobalEventEmitter.emit('appContentReady');

    navigation.addListener('blur', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
      }
    });

    navigation.addListener('focus', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(true)
      }
    });

    const resumeListener = PageEventEmitter.addListener('onResume', () => {
      updateWelfare();
    });

    return () => {
      resumeListener?.remove();
    }
  }, []);

  function navigateToHome() {
    GlobalEventEmitter.emit('appContentLoading')
    navigation.replace('Home')
  }

  function handleScrollBeginDrag() {
    GlobalEventEmitter.emit(ScrollEventName.onScrollBeginDrag);
  }

  function handleScrollEndDrag() {
    GlobalEventEmitter.emit(ScrollEventName.onScrollEndDrag);
  }

  function handleMomentumScrollBegin() {
    GlobalEventEmitter.emit(ScrollEventName.onMomentumScrollBegin);
  }

  function handleMomentumScrollEnd() {
    GlobalEventEmitter.emit(ScrollEventName.onMomentumScrollEnd);
  }

  return (
    <View style={[styles.container]}>
      {/* <Header
        onTabPress={navigateToHome}
        onLayout={setHeaderHeight}
        singleTab={isHarmony}
        hideTitle={nativeInfo?.embed}
      /> */}
      <ScrollAnalyticWapper
        id={coinCenterId}
        viewStyle={{ flex: 1 }}
        //@ts-ignore
        useNavigation={useNavigation}
      >
        <Animated.ScrollView
          style={[styles.scrollView]}
          contentContainerStyle={styles.scrollContent}
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onScrollEndDrag={handleScrollEndDrag}
          onMomentumScrollBegin={handleMomentumScrollBegin}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <BetterImage
            source={{
              uri: theme.headerBg
            }}
            style={styles.headerBg}
          />
          <View style={{ height: headerHeight }}></View>
          <AnnouncementBanner />
          <View style={styles.content}>
            <Balance showSignModal={showSignModal} />
            {showSignModal? (<BetterImage
              source={{
                uri: theme.splitLine
              }}
              imgWidth={343}
              imgHeight={9}
              style={styles.splitLine}
            />) : null}
            <SignIn adHeight={adHeight} setShowSignModal={setShowSignModal} />
          </View>
          {/* <ShareCoins /> */}
          <ValuableTask />
          <ListenTask />
          <VideoTask />
          <FlipCard />
          {/* <DailyTask /> */}
          <CheckInCollapse adHeight={adHeight} />
          {/* <ShareCoinsCollapse /> */}
          <Rule />
        </Animated.ScrollView>
      </ScrollAnalyticWapper>
      <AdPopWindow adHeight={adHeight} setAdHeight={setAdHeight} />
      <ListenTaskModal visible={true} onClose={() => {}} type="withAd" />
      <RedPacketRain visible={showRedPacketRain} />
      <TouchTask />
    </View>
  )
}