import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { StyleSheet } from 'react-native';
import { px } from '../../utils/px';

interface RegionPickerProps {
  province: string;
  city: string;
  district: string;
  onRegionChange: (data: { province: string; city: string; district: string }) => void;
  styles: any;
  theme: any;
}

const RegionPicker: React.FC<RegionPickerProps> = ({
  province,
  city,
  district,
  onRegionChange,
  styles,
  theme,
}) => {
  const [showFirstPicker, setShowFirstPicker] = useState(false);
  const [showSecondPicker, setShowSecondPicker] = useState(false);
  const [showThirdPicker, setShowThirdPicker] = useState(false);
  const [firstValue, setFirstValue] = useState(province);
  const [secondValue, setSecondValue] = useState(city);
  const [thirdValue, setThirdValue] = useState(district);

  // 使用省市区数据
  const pcaTestData = require('./pca.json');
  const firstOptions = Object.keys(pcaTestData);
  const secondOptions: Record<string, string[]> = {};
  const thirdOptions: Record<string, Record<string, string[]>> = {};
  
  // 构建二级和三级数据结构
  firstOptions.forEach(prov => {
    secondOptions[prov] = Object.keys(pcaTestData[prov] || {});
    thirdOptions[prov] = {};
    secondOptions[prov].forEach(ct => {
      thirdOptions[prov][ct] = pcaTestData[prov][ct] || [];
    });
  });

  // 同步外部数据变化
  useEffect(() => {
    setFirstValue(province);
    setSecondValue(city);
    setThirdValue(district);
  }, [province, city, district]);

  const handleFirstChange = (value: string) => {
    setFirstValue(value);
    setSecondValue('');
    setThirdValue('');
    setShowFirstPicker(false);
    onRegionChange({ province: value, city: '', district: '' });
  };

  const handleSecondChange = (value: string) => {
    setSecondValue(value);
    setThirdValue('');
    setShowSecondPicker(false);
    onRegionChange({ province: firstValue, city: value, district: '' });
  };

  const handleThirdChange = (value: string) => {
    setThirdValue(value);
    setShowThirdPicker(false);
    onRegionChange({ province: firstValue, city: secondValue, district: value });
  };

  return (
    <>
      {/* 省份选择器 */}
      <View style={styles.formItem}>
        <Text style={styles.label}>选择省份</Text>
        <TouchableOpacity
          style={styles.regionSelector}
          onPress={() => {
            setShowFirstPicker(!showFirstPicker);
            setShowSecondPicker(false);
            setShowThirdPicker(false);
          }}
          activeOpacity={1}
        >
          <Text style={styles.regionText}>
            {firstValue || '请选择省份'}
          </Text>
          <Image source={theme.prizeInfoForm.arrow_icon} style={styles.arrowIcon} />
        </TouchableOpacity>
      </View>

      {/* 省份选项 */}
      {showFirstPicker && (
        <View style={localStyles.pickerContainer}>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={true}
            bounces={false}
          >
            {firstOptions.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  localStyles.option,
                  index < firstOptions.length - 1 && localStyles.optionBorder
                ]}
                onPress={() => handleFirstChange(item)}
              >
                <Text style={[
                  localStyles.optionText,
                  firstValue === item && localStyles.selectedText
                ]}>
                  {item}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* 城市选择器 */}
      <View style={[styles.formItem, { opacity: firstValue ? 1 : 0.5 }]}>
        <Text style={styles.label}>选择城市</Text>
        <TouchableOpacity
          style={styles.regionSelector}
          onPress={() => {
            if (firstValue) {
              setShowSecondPicker(!showSecondPicker);
              setShowFirstPicker(false);
              setShowThirdPicker(false);
            }
          }}
          activeOpacity={firstValue ? 1 : 0.5}
        >
          <Text style={styles.regionText}>
            {secondValue || (firstValue ? '请选择城市' : '请先选择省份')}
          </Text>
          <Image source={theme.prizeInfoForm.arrow_icon} style={styles.arrowIcon} />
        </TouchableOpacity>
      </View>

      {/* 城市选项 */}
      {showSecondPicker && firstValue && (
        <View style={localStyles.pickerContainer}>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={true}
            bounces={false}
          >
            {(secondOptions[firstValue] || []).map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  localStyles.option,
                  index < (secondOptions[firstValue]?.length || 0) - 1 && localStyles.optionBorder
                ]}
                onPress={() => handleSecondChange(item)}
              >
                <Text style={[
                  localStyles.optionText,
                  secondValue === item && localStyles.selectedText
                ]}>
                  {item}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* 区县选择器 */}
      <View style={[styles.formItem, { opacity: secondValue ? 1 : 0.5 }]}>
        <Text style={styles.label}>选择区县</Text>
        <TouchableOpacity
          style={styles.regionSelector}
          onPress={() => {
            if (secondValue) {
              setShowThirdPicker(!showThirdPicker);
              setShowFirstPicker(false);
              setShowSecondPicker(false);
            }
          }}
          activeOpacity={secondValue ? 1 : 0.5}
        >
          <Text style={styles.regionText}>
            {thirdValue || (secondValue ? '请选择区县' : '请先选择城市')}
          </Text>
          <Image source={theme.prizeInfoForm.arrow_icon} style={styles.arrowIcon} />
        </TouchableOpacity>
      </View>

      {/* 区县选项 */}
      {showThirdPicker && secondValue && firstValue && (
        <View style={localStyles.pickerContainer}>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={true}
            bounces={false}
          >
            {(thirdOptions[firstValue]?.[secondValue] || []).map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  localStyles.option,
                  index < (thirdOptions[firstValue]?.[secondValue]?.length || 0) - 1 && localStyles.optionBorder
                ]}
                onPress={() => handleThirdChange(item)}
              >
                <Text style={[
                  localStyles.optionText,
                  thirdValue === item && localStyles.selectedText
                ]}>
                  {item}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </>
  );
};

const localStyles = StyleSheet.create({
  pickerContainer: {
    marginHorizontal: 16,
    marginTop: -10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#fff',
    maxHeight: 150,
  },
  option: {
    padding: 15,
  },
  optionBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedText: {
    color: '#FF4444',
    fontWeight: '600',
  },
});

export default RegionPicker;