import React, { useState, useContext, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
  Modal,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ThemeContext } from '../../contextV2/themeContext';
import { NativeInfoContext } from '../../contextV2/nativeInfoContext';
import { PrizeInfoFormData } from './types';
import { getStyles } from './styles';
import { RootStackParamList } from '../../router/type';
import { StackScreenProps } from '@react-navigation/stack';
import BackBtn from '../../componentsV2/common/BackBtn';
import { updateRotaryTableAwardInfo } from '../../services/welfare/rewardRecord';
import RegionPicker from './RegionPicker';

export default function PrizeInfoForm(props: StackScreenProps<RootStackParamList>) {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const theme = useContext(ThemeContext);
  const nativeInfo = useContext(NativeInfoContext);
  const styles = getStyles(theme);
    // 获取路由参数
  const { awardCode, orderNo } = props.route.params as {
    awardCode: string;
    orderNo: string;
  };

  // 判断是否隐藏 header
  const hideHeader = nativeInfo.embed === '1';

  const paddingTop = 10 + insets.top;

  const [formData, setFormData] = useState<PrizeInfoFormData>({
    realName: '',
    phoneNumber: '',
    province: '',
    city: '',
    district: '',
    detailAddress: '',
  });

  // 提交状态
  const [isSubmitting, setIsSubmitting] = useState(false);


  // 检查表单是否完整填写
  const isFormValid = useMemo(() => {
    return (
      formData.realName.trim() !== '' &&
      formData.phoneNumber.trim() !== '' &&
      formData.phoneNumber.length === 11 && // 只需要保证手机号长度合规
      formData.province !== '' &&
      formData.city !== '' &&
      formData.district !== '' &&
      formData.detailAddress.trim() !== ''
    );
  }, [formData]);

  const handleSubmit = async () => {
    if (isSubmitting) return;

    // 验证表单
    if (!formData.realName.trim()) {
      Alert.alert('提示', '请输入真实姓名');
      return;
    }

    if (!formData.phoneNumber.trim()) {
      Alert.alert('提示', '请输入手机号');
      return;
    }

    // 验证手机号长度
    if (formData.phoneNumber.length !== 11) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (!formData.province || !formData.city || !formData.district) {
      Alert.alert('提示', '请选择省市区');
      return;
    }

    if (!formData.detailAddress.trim()) {
      Alert.alert('提示', '请输入详细地址');
      return;
    }

    try {
      setIsSubmitting(true);

      // 调用接口
      const response = await updateRotaryTableAwardInfo({
        orderNo: orderNo,
        address: `${formData.province}${formData.city}${formData.district}${formData.detailAddress}`,
        name: formData.realName,
        mobile: `+86${formData.phoneNumber}`,
      });

      if (response?.data?.success) {
        Alert.alert('提交成功', '您的信息已提交，我们会尽快处理', [
          {
            text: '确定',
            onPress: () => navigation.goBack(),
          },
        ]);
      } else {
        Alert.alert('提交失败', '请稍后重试');
      }
    } catch (error) {
      console.error('提交失败:', error);
      Alert.alert('提交失败', '请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };


  const updateFormData = (field: keyof PrizeInfoFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };


  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      {/* 头部 */}
      <View style={[styles.header, { paddingTop }, hideHeader ? { opacity: 0 } : null]}>
        <View style={[styles.backBtn, { top: paddingTop }]}>
          {hideHeader ? null : <BackBtn onPress={navigation.goBack} />}
        </View>
        <Text style={styles.title}> </Text>
      </View>

      <ScrollView 
        style={{ flex: 1 }} 
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.content}>
          {/* 页面标题 */}
          <View>
            <Text style={styles.pageTitleText}>请填写中奖信息</Text>
          </View>

          {/* 兑换码 */}
          <View style={styles.formItemWithBorder}>
            <Text style={styles.label}>兑换码</Text>
            <Text style={styles.codeValue}>{awardCode}</Text>
          </View>

          {/* 真实姓名 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>真实姓名</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor={theme.prizeInfoForm.placeholder_text_color}
                value={formData.realName}
                onChangeText={(text) => updateFormData('realName', text)}
                maxLength={20}
              />
            </View>
          </View>

          {/* 手机号 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>手机号</Text>
            <View style={styles.phoneInputContainer}>
              <Text style={styles.phonePrefix}>+86</Text>
              <View style={styles.phoneSeparator} />
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor={theme.prizeInfoForm.placeholder_text_color}
                value={formData.phoneNumber}
                onChangeText={(text) => updateFormData('phoneNumber', text)}
                keyboardType="numeric"
                maxLength={11}
              />
            </View>
          </View>

          {/* 省市区选择器 */}
          <RegionPicker
            province={formData.province}
            city={formData.city}
            district={formData.district}
            onRegionChange={(data) => {
              setFormData(prev => ({
                ...prev,
                ...data,
              }));
            }}
            styles={styles}
            theme={theme}
          />

          {/* 详细地址 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>详细地址</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor={theme.prizeInfoForm.placeholder_text_color}
                value={formData.detailAddress}
                onChangeText={(text) => updateFormData('detailAddress', text)}
                multiline
                textAlignVertical="top"
              />
            </View>
          </View>
        </View>

        {/* 警告提示 */}
        <View style={styles.warning}>
          <Text style={styles.warningText}>
          信息提交后无法修改，请确保收货信息准确无误
          </Text>
        </View>

        {/* 提交按钮 */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            isFormValid && styles.submitButtonActive
          ]}
          onPress={handleSubmit}
          activeOpacity={1}
          disabled={!isFormValid || isSubmitting}
        >
          <Text style={styles.submitButtonText}>
            {isSubmitting ? '提交中...' : '提交'}
          </Text>
        </TouchableOpacity>
      </ScrollView>

    </KeyboardAvoidingView>
  );
}