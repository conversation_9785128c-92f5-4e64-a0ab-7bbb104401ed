import React, { useContext, useState, useEffect } from 'react';
import { View, Text, Image, FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getStyles } from './styles';
import { ThemeContext } from '../../contextV2/themeContext';
import { NativeInfoContext } from '../../contextV2/nativeInfoContext';
import BackBtn from '../../componentsV2/common/BackBtn';
import reward_nodata from '../../appImagesV2/reward_nodata';
import reward_record_warning from '../../appImagesV2/reward_record_warning';
import GlobalEventEmitter from '../../utilsV2/globalEventEmitter';
import { usePageReport } from '../../hooks/usePageReport';
import { RootStackParamList } from '../../router/type';
import { StackScreenProps } from '@react-navigation/stack';
import { RewardRecord as RewardRecordType } from './types';
import RewardRecordItem from './components/RewardRecordItem';
import { queryMyRotaryTableAward } from '../../services/welfare/rewardRecord';
import { Page } from '@xmly/rn-sdk/dist/Page';
import getUrlToOpen from '@xmly/rn-sdk/dist/Navigate';
import getXMRequestId from '../../utilsV2/getXMRequestId';
import xmlog from '../../utilsV2/xmlog';

export default function RewardRecord(props: StackScreenProps<RootStackParamList>) {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const theme = useContext(ThemeContext);
  const nativeInfo = useContext(NativeInfoContext);
  const styles = getStyles(theme);

  // 使用简单的useState管理状态
  const [rewardList, setRewardList] = useState<RewardRecordType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [xmRequestId, setXmRequestId] = useState('');

  // 判断是否隐藏 header
  const hideHeader = nativeInfo.embed === '1';

  // 生成 xmRequestId
  useEffect(() => {
    (async () => {
      const reqId = await getXMRequestId();
      setXmRequestId(reqId);
    })();
  }, []);

  // 页面上报
  usePageReport({
    pageViewCode: 69159,
    pageExitCode: 69160,
    currPage: 'myPrizePage',
    otherProps: props,
  });

  // 获取奖品记录数据
  const fetchRewardRecord = async () => {
    try {
      setLoading(true);
      const response = await queryMyRotaryTableAward();
      if (response?.data?.awardInfos) {
        // 直接使用接口返回的数据，不需要转换
        setRewardList(response.data.awardInfos);
      } else {
        setRewardList([]);
      }
    } catch (error) {
      console.error('获取奖品记录失败:', error);
      setRewardList([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取按钮文案
  const getButtonText = (status: number) => {
    switch (status) {
      case 2: // 未认领
        return '填写地址，领取奖品';
      case 6: // 物流中
        return '物流详情';
      default:
        return '联系客服，领取奖品';
    }
  };

  // 处理奖品记录点击
  const handleRecordPress = (item: RewardRecordType) => {
    xmlog.click(69161, undefined, {
      Item: getButtonText(item.status),
      xmRequestId,
    });
    if (item.status === 2) {
      navigation.navigate('PrizeInfoForm', {
        awardCode: item.awardCode,
        orderNo: item.orderNo
      });
    } else if (item.status === 6) {
      // 状态为6时，跳转到物流详情页面
      if (item.expressLink) {
        Page.start(getUrlToOpen(item.expressLink));
      }
    }
  };

  // 监听页面焦点变化，包括首次展示和二次曝光
  useEffect(() => {
    // 通知根组件页面内容已准备就绪，可以隐藏骨架屏
    GlobalEventEmitter.emit('appContentReady');

    // 首次进入页面时立即加载数据
    fetchRewardRecord();

    // 监听页面获得焦点（主要用于从其他页面返回时）
    const focusListener = navigation.addListener('focus', () => {
      // 页面获得焦点时加载数据（从其他页面返回时）
      fetchRewardRecord();
    });

    // 清理监听器
    return () => {
      focusListener?.();
    };
  }, [navigation]);

  const paddingTop = 10 + insets.top;

  // 渲染列表项
  const renderItem = ({ item }: { item: RewardRecordType }) => (
    <RewardRecordItem item={item} onPress={handleRecordPress} />
  );

  // 渲染空状态
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Image source={reward_nodata} style={styles.emptyImage} />
      <Text style={styles.emptyText}>空空如也</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { paddingTop }, hideHeader ? { opacity: 0 } : null]}>
        <View style={[styles.backBtn, { top: paddingTop }]}>
          {hideHeader ? null : <BackBtn onPress={navigation.goBack} />}
        </View>
        <Text style={styles.title}>实物奖品记录</Text>
      </View>

      {/* Warning Notice */}
      {rewardList.length > 0 && (
        <View style={styles.warningContainer}>
          <Image source={reward_record_warning} style={styles.warningIcon} />
          <Text style={styles.warningText}>
            本页面仅展示实物奖品记录，金币奖励请到收支记录查看
          </Text>
        </View>
      )}

      {/* Content */}
      {rewardList.length > 0 ? (
        <FlatList
          style={styles.listContainer}
          contentContainerStyle={styles.listContent}
          data={rewardList}
          renderItem={renderItem}
          keyExtractor={(item) => item.orderNo}
          showsVerticalScrollIndicator={false}
        />
      ) : loading ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>加载中...</Text>
        </View>
      ) : (
        renderEmptyState()
      )}
    </View>
  );
}
