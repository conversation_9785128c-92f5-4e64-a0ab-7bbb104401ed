import { StyleSheet, Platform } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";
import { darkTheme as commonDarkTheme } from "../common/theme";

export const getStyles = (theme: typeof darkTheme & typeof commonDarkTheme) => StyleSheet.create({
  container: {
    marginTop: px(20),
  },
  head: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: px(16),
  },
  title: {
    fontSize: px(14),
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    color: theme.titleColor,
    lineHeight: px(20),
  },
  steps: {
    overflow: 'scroll',
    flexDirection: 'row',
    marginTop: px(17),
  },
  paddingLeft: {
    width: px(16),
  },
  paddingRight: {
    width: px(8),
  },
  completedText: {
    fontSize: px(11),
    color: theme.completedTextColor,
  },
  step: {
    backgroundColor: theme.stepBgColor,
    borderRadius: px(16),
    height: px(58),
    alignItems: 'center',
  },
  currentStep: {
    backgroundColor: theme.stepCurrentBgColor,
  },
  stepCompleted: {
    backgroundColor: theme.stepCompletedBgColor,
  },
  stepFuture: {
    backgroundColor: theme.stepFutureBgColor,
  },
  icon: {
    width: px(24),
    height: px(24),
    marginBottom: px(2),
    marginTop: px(8),
  },
  iconFuture: {
    // opacity: 0.5,
    opacity: 1,
  },
  completedCenterIcon: {
    width: px(24),
    height: px(24),
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginTop: px(-12),
    marginLeft: px(-12),
  },
  amount: {
    fontSize: px(10),
    color: theme.amountColor,
    lineHeight: px(16),
  },
  amountRedPacket: {
    fontFamily: 'XmlyNumber',
    fontSize: px(14),
    color: theme.amountColor,
    fontWeight: '600',
    lineHeight: px(16),
  },
  amountFuture: {
    color: theme.amountColor,
    lineHeight: px(16),
  },
  amountCurrent: {
    fontFamily: 'XmlyNumber',
    fontSize: px(14),
    fontWeight: '600',
    lineHeight: px(16),
  },
  shadow: {
    position: 'absolute',
    right: 0,
    bottom: 6,
    width: px(10),
    height: px(68),
  },
});
