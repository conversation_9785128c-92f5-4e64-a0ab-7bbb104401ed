import { theme<PERSON>tom } from 'atom/theme';
import { atom } from 'jotai';
import commonThemeAtom from '../common/theme';

export enum VideoTaskStatus {
  UNRECEIVED = 1,
  RECEIVED = 2
}

export const darkTheme = {
  titleColor: '#FFFFFF',
  buttonBgColor: 'rgba(255, 68, 68, .08)',
  buttonTextColor: '#FF4444',
  stepBgColor: 'rgba(255, 129, 129, 0.12)',
  stepCompletedBgColor: '#353535',
  stepCurrentBgColor: '#6B6047',
  stepFutureBgColor: '#353535',
  stepBorderColor: 'rgba(255, 215, 215, .3)',
  amountColor: '#FFFFFF',
  completeTextColor: 'rgba(255, 255, 255, .5)',
  coinIcon: 'https://imagev2.xmcdn.com/storages/6940-audiofreehighqps/7B/BC/GAqh4zILsZIEAAAHpQOCKX74.png',
  completedIcon: 'https://imagev2.xmcdn.com/storages/e5a5-audiofreehighqps/37/DE/GKwRIDoLAPC6AAALoAMq1MZp.png',
  shadowGradientColors: ['rgba(31, 31, 31, 0)', '#1F1F1F'],
  shadowGradientLocations: [0.1, 1],
  completedTextColor: 'rgba(255, 255, 255, .3)',
}

const lightTheme = {
  titleColor: '#111111',
  buttonBgColor: 'rgba(255, 68, 68, .08)',
  buttonTextColor: '#FF4444',
  stepBgColor: 'rgba(255, 129, 129, 0.05)',
  stepCompletedBgColor: '#F4F6FA',
  stepCurrentBgColor: '#FFF5DE',
  stepFutureBgColor: '#F4F6FA',
  stepBorderColor: '#D0AE60',
  amountColor: '#240000',
  completeTextColor: 'rgba(36, 0, 0, .5)',
  coinIcon: 'https://imagev2.xmcdn.com/storages/6940-audiofreehighqps/7B/BC/GAqh4zILsZIEAAAHpQOCKX74.png',
  completedIcon: 'https://imagev2.xmcdn.com/storages/e5a5-audiofreehighqps/37/DE/GKwRIDoLAPC6AAALoAMq1MZp.png',
  shadowGradientColors: ['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 1)'],
  shadowGradientLocations: [0, 1],
  completedTextColor: 'rgba(44, 44, 60, 0.3)',
}

const videoTaskThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  const commonTheme = get(commonThemeAtom);
  return {
    ...commonTheme,
    ...(theme === 'dark' ? darkTheme : lightTheme)
  };
})

export default videoTaskThemeAtom; 