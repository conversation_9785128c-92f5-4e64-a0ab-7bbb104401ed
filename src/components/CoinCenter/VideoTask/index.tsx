import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { ScrollView, View, Text, Image } from 'react-native'
import { XMAppVersionHelper } from '@xmly/rn-sdk'
import { Touch } from '@xmly/rn-components'
import { getStyles } from './styles'
import { useAtomValue, useSetAtom } from 'jotai'
import videoTaskThemeAtom, { VideoTaskStatus } from './theme'

import TaskButton from '../common/TaskButton'
import ModuleCard from '../common/ModuleCard'
import LinearGradient from 'react-native-linear-gradient'
import Title from '../common/Title'
import watchAd from 'utils/watchAd'
import { updateWelfareAtom } from 'atom/welfare'
import { AD_SOURCE, RewardType } from 'constants/ad'
import { videoTaskAtom, writeVideoTaskAtom } from './store'
import xmlog from 'utilsV2/xmlog'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { theme<PERSON>tom } from 'atom/theme'
import iconVideoTaskCoin from 'appImagesV2/icon_video_task_coin'
import iconVideoTaskRedEnvelope from 'appImagesV2/icon_video_task_red_envelope'
import iconVideoTaskFinishDark from 'appImagesV2/icon_video_task_finish_dark'
import iconVideoTaskFinishLight from 'appImagesV2/icon_video_task_finish_light'

const VideoTask = forwardRef(
  (
    {
      onFinishVideo,
    }: {
      onFinishVideo: (cadId?: number | undefined, adResponseId?: number, rewardCoin?: number, fallbackReq?: number, ecpm?: string) => void
    },
    ref
  ) => {
    const theme = useAtomValue(videoTaskThemeAtom)
    const appTheme = useAtomValue(themeAtom)
    const styles = getStyles(theme)
    const scrollRef = React.useRef<ScrollView>(null)
    const [rendered, setRendered] = useState(false)
    const [isValidVersion, setIsValidVersion] = useState(false)
    const stepWidth = 52
    const stepPadding = 6
    const updateWelfare = useSetAtom(updateWelfareAtom)
    const videoTasks = useAtomValue(videoTaskAtom)
    const getVideoTasks = useSetAtom(writeVideoTaskAtom)
    const list = videoTasks?.list ?? []
    const reversedLastCompletedIndex = list
      .slice()
      .reverse()
      .findIndex((task) => task.status === VideoTaskStatus.RECEIVED)
    const lastCompletedIndex = reversedLastCompletedIndex === -1 ? -1 : list.length - 1 - reversedLastCompletedIndex
    const currentIndex = Math.min(lastCompletedIndex + 1, list.length - 1)
    const completed = lastCompletedIndex === list.length - 1

    // 获取图标
    const getTaskIcon = (task: any, stepCompleted: boolean) => {
      if (stepCompleted) {
        return appTheme === 'dark' ? iconVideoTaskFinishDark : iconVideoTaskFinishLight
      }
      return task.awardType === 2 ? iconVideoTaskRedEnvelope : iconVideoTaskCoin
    }

    // 获取文字内容
    const getTaskText = (task: any, stepCompleted: boolean, isCurrent: boolean) => {
      if (stepCompleted) {
        return null // 已完成任务不显示文字
      }
      // 金币类型的任务
      if (task.awardType === 1) {
        return isCurrent ? task.awardText : '待解锁'
      }
      // 红包类型的任务统一显示awardText
      return task.awardText
    }

    useEffect(() => {
      if (currentIndex >= 0 && rendered) {
        scrollRef.current?.scrollTo({ x: currentIndex * (stepWidth + stepPadding), animated: true })
      }
    }, [rendered, currentIndex])

    useEffect(() => {
      getVideoTasks()
    }, [])

    useEffect(() => {
      async function checkVersion() {
        const isHigherVersion = await XMAppVersionHelper.notLowerThan('9.3.75')
        console.log('isHigherVersion', isHigherVersion)
        setIsValidVersion(isHigherVersion)
      }
      checkVersion()
    }, [setIsValidVersion])

    async function handleWatchVideo() {
      // 福利中心-看视频  点击事件
      xmlog.click(67728, 'VideoTask', { currPage: 'welfareCenter' })
      if (completed) {
        return
      }
      try {
        const res = await watchAd({
          positionName: 'incentive_welfare',
          slotId: 307,
          rewardVideoStyle: !isValidVersion ? 1 : 0,
          sourceName: AD_SOURCE.AD_VIDEO,
          rewardType: RewardType.AD_VIDEO,
          coins: list[currentIndex].coins,
        })
        console.log('res', res)
        if (res.success && isValidVersion) {
          onFinishVideo(res.adId, res.adResponseId, list[currentIndex].coins, res.fallbackReq, res.ecpm)
        }
        getVideoTasks()
        updateWelfare()
      } catch (error) {
        console.error('debug_handleWatchVideo', error)
      }
    }

    function onShow() {
      // 福利中心-看视频  控件曝光
      xmlog.event(67799, 'slipPage', { currPage: 'welfareCenter' })
    }

    useImperativeHandle(ref, () => ({
      nextVideo() {
        handleWatchVideo()
      },
      finishVidoeReward() {
        getVideoTasks()
        updateWelfare()
      },
    }))

    return (
      <ScrollAnalyticComp
        itemKey="VideoTask"
        onShow={onShow}
      >
        <ModuleCard style={styles.container}>
          <View style={styles.head}>
            <Title
              title="看广告得金币"
              style={styles.title}
            />
            {completed ? (
              <Text style={styles.completedText}>明日再来</Text>
            ) : (
              <TaskButton
                text="看广告"
                onPress={handleWatchVideo}
              />
            )}
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.steps}
            ref={scrollRef}
          >
            <View
              style={styles.paddingLeft}
              onLayout={() => setRendered(true)}
            />
            {videoTasks?.list?.map?.((task, index) => {
              const stepCompleted = task.status === VideoTaskStatus.RECEIVED
              const isFuture = index > currentIndex
              const isCurrent = index === currentIndex && !stepCompleted
              const taskIcon = getTaskIcon(task, stepCompleted)
              const taskText = getTaskText(task, stepCompleted, isCurrent)

              return (
                <Touch
                  onPress={handleWatchVideo}
                  disabled={stepCompleted}
                  style={[
                    styles.step,
                    { 
                      width: stepWidth, 
                      marginRight: stepPadding,
                    },
                    stepCompleted ? styles.stepCompleted : null,
                    isFuture ? styles.stepFuture : null,
                    isCurrent ? styles.currentStep : null,
                  ]}
                  key={index}
                >
                  {stepCompleted ? (
                    // 已完成任务：在视图中央显示对钩图标
                    <Image
                      source={taskIcon}
                      style={[styles.icon, styles.completedCenterIcon]}
                    />
                  ) : (
                    // 未完成任务：显示对应的图标和文字
                    <>
                      <Image
                        source={taskIcon}
                        style={[styles.icon, index !== currentIndex || stepCompleted ? styles.iconFuture : null]}
                      />
                      {taskText && (
                        <Text style={[
                          task.awardType === 2 ? styles.amountRedPacket : styles.amount, 
                          isFuture ? styles.amountFuture : null,
                          isCurrent ? styles.amountCurrent : null
                        ]}>{taskText}</Text>
                      )}
                    </>
                  )}
                </Touch>
              )
            })}
            <View style={styles.paddingRight} />
          </ScrollView>
          <LinearGradient
            useAngle={true}
            angle={90}
            colors={theme.shadowGradientColors}
            locations={theme.shadowGradientLocations}
            style={styles.shadow}
          />
        </ModuleCard>
      </ScrollAnalyticComp>
    )
  }
)
export default VideoTask
