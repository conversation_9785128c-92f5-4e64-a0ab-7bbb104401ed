import { atom } from 'jotai';
import { queryListenTaskInfo, ListenTaskInfo } from 'services/welfare/listenTask';
import userInfoDetail from 'modulesV2/userInfoDetail';
import { NativeModules, NativeEventEmitter } from 'react-native';
import { PageEventEmitter } from 'defs';

const { ListenTime, WelfareLifecycle } = NativeModules;

// 生命周期事件监听器管理
let lifecycleListener: any = null;

// 初始状态
const initialListenTaskInfo: ListenTaskInfo = {
  success: true,
  currentStep: 0,
  stepInfo: [],
  totalCoins: 0,
  listenDuration: 0,
  nextRewardTime: 30,
  title: '',
  btnText: '',
  status: 0 // 添加初始状态
};

// 创建状态原子
export const listenTaskAtom = atom<ListenTaskInfo>(initialListenTaskInfo);

// 写入/更新任务状态的原子
export const writeListenTaskAtom = atom(
  null,
  async (get, set) => {
    const task = get(listenTaskAtom)

    // 检查用户登录状态
    const userInfo = userInfoDetail.getDetail();

    if (!userInfo?.isLogin) {
      set(listenTaskAtom, {...task, success: false});
      return;
    }

    try {
      // 获取当前的听书时长作为 localDuration
      const res = await ListenTime.getListenDuration();
      const localDuration = res?.listenDuration;
      console.log('📊 [STORE] 当前听书时长 (localDuration):', localDuration);
      console.log('📊 [STORE] localDuration 类型:', typeof localDuration);

      const response = await queryListenTaskInfo(localDuration);
      if (response?.data) {
        set(listenTaskAtom, {...task, ...response.data});
      } else {
        set(listenTaskAtom, {...task, success: false});
      }
    } catch (error) {
      console.error('Failed to update listen task:', error);
      set(listenTaskAtom, {...task, success: false})
    }
  }
);

// 更新听书进度的原子
export const updateListenProgressAtom = atom(
  null,
  async (get, set) => {
    const currentTask = get(listenTaskAtom);
    if (!currentTask) return;

    // 更新听书时长和进度
    const newDuration = currentTask.listenDuration + 1; // 每次增加1秒
    const currentStep = currentTask.currentStep;
    const stepInfo = [...currentTask.stepInfo];

    // 检查是否达到下一个奖励节点
    if (newDuration >= currentTask.nextRewardTime && currentStep < stepInfo.length - 1) {
      stepInfo[currentStep].status = 1; // 更新为待领取状态
      stepInfo[currentStep + 1].status = 0; // 下一个任务初始化为未完成状态
      
      set(listenTaskAtom, {
        ...currentTask,
        currentStep: currentStep + 1,
        stepInfo,
        listenDuration: newDuration,
        nextRewardTime: getNextRewardTime(currentStep + 1)
      });
    } else {
      set(listenTaskAtom, {
        ...currentTask,
        listenDuration: newDuration
      });
    }
  }
);


// 获取下一个奖励所需时间
function getNextRewardTime(stepIndex: number): number {
  const rewardTimes = [30, 60, 300, 600, 900]; // 30秒, 1分钟, 5分钟, 10分钟, 15分钟
  return rewardTimes[stepIndex] || 0;
}

// 初始化生命周期监听器的原子 (支持 WelfareLifecycle 和 PageEventEmitter 两种方式)
export const initWelfareLifecycleListenerAtom = atom(
  null,
  async (_, set) => {
    // 如果已经有监听器，先清理
    if (lifecycleListener) {
      console.info('debug_lifecycle_cleanup', '清理现有的生命周期监听器');
      lifecycleListener.remove();
      lifecycleListener = null;
    }

    // 检查可用的模块
    console.info('debug_lifecycle_check', '检查生命周期模块', {
      WelfareLifecycle: !!WelfareLifecycle,
      PageEventEmitter: !!PageEventEmitter,
      availableModules: Object.keys(NativeModules).filter(key =>
        key.includes('Welfare') || key.includes('Lifecycle') || key.includes('Page')
      )
    });

    // 定义回调函数
    const onResumeCallback = async () => {
      console.info('debug_lifecycle_onResume', '生命周期 onResume 回调触发，重新请求收听任务数据');

      try {
        // 重新获取收听任务数据
        await set(writeListenTaskAtom);
        console.info('debug_lifecycle_onResume_success', '收听任务数据刷新成功');
      } catch (error) {
        console.error('debug_lifecycle_onResume_error', '刷新收听任务数据失败', error);
      }
    };

    try {
      // 优先尝试使用 WelfareLifecycle
      console.info('debug_lifecycle_init', 'WelfareLifecycle =' + WelfareLifecycle);
      if (WelfareLifecycle) {
        console.info('debug_lifecycle_init', '使用 WelfareLifecycle 初始化 onResume 监听器');
        const eventEmitter = new NativeEventEmitter(WelfareLifecycle);
        lifecycleListener = eventEmitter.addListener('onResume', onResumeCallback);
        console.info('debug_lifecycle_success', 'WelfareLifecycle 监听器初始化成功');
      }
      // 备用方案：使用 PageEventEmitter
      else if (PageEventEmitter) {
        console.info('debug_lifecycle_init', 'WelfareLifecycle2 =' + WelfareLifecycle);
        console.info('debug_lifecycle_init', '使用 PageEventEmitter 初始化 onResume 监听器');
        lifecycleListener = PageEventEmitter.addListener('onResume', onResumeCallback);
        console.info('debug_lifecycle_success', 'PageEventEmitter 监听器初始化成功');
      }
      // 都不可用
      else {
        console.warn('debug_lifecycle_unavailable', '没有可用的生命周期模块', {
          allNativeModules: Object.keys(NativeModules)
        });
      }
    } catch (error) {
      console.error('debug_lifecycle_error', '初始化生命周期监听器失败', error);
    }
  }
);

// 清理生命周期监听器的原子
export const cleanupWelfareLifecycleListenerAtom = atom(
  null,
  () => {
    if (lifecycleListener) {
      console.info('debug_lifecycle_cleanup', '清理生命周期监听器');
      lifecycleListener.remove();
      lifecycleListener = null;
    }
  }
);

