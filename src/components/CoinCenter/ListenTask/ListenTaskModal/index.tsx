import React, { useEffect, useState, useCallback, useRef } from 'react';
import { View, Animated, TouchableWithoutFeedback, Easing, Modal as RNModal, NativeModules } from 'react-native';
import { useAtomValue, useAtom } from 'jotai';
import { Toast } from "@xmly/rn-sdk";
import { themeAtom } from 'atom/theme';
import { showRewardModalAtom } from 'atom/listenTaskModal'
import { useVideoResources } from 'hooks/useVideoResources';
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import { LISTEN_TASK_POSITION, AD_SOURCE, RewardType, FallbackReqType } from "constants/ad";
import { encryptLocalDuration } from 'services/welfare/listenTask';
import watchAd from "utils/watchAd";
import { getStyles } from './styles';
import RewardModalContent from 'components/CoinCenter/RewardModalContent';
import ConfettiAnimation from 'components/CoinCenter/common/ConfettiAnimation';
import CloseIcon from 'componentsV2/common/CloseIcon';
import AdRewardModalContent from 'components/CoinCenter/AdRewardModalContent';
const customEasing = Easing.bezier(0.66, 0, 0.34, 1);

// 视频资源链接
export const VIDEOS = {
  BACKGROUND_START: 'https://aod.cos.tx.xmcdn.com/storages/e783-audiofreehighqps/30/A0/GKwRIaILzX4rAANaOAOUBfnN.mp4',
  BACKGROUND_LOOP: 'https://aod.cos.tx.xmcdn.com/storages/2f20-audiofreehighqps/03/59/GKwRIJILzX4rAAQDGQOUBflt.mp4',
};

// 背景开始视频预计时长（毫秒）
const BACKGROUND_START_DURATION = 2000; // 根据实际视频长度调整
// 预加载背景循环的提前时间（毫秒）
const PRELOAD_LOOP_TIME = 100;

enum VideoPlayState {
  BACKGROUND_START_AND_CONFETTI,
  BACKGROUND_LOOP,
  FINISHED
}

// 导出预加载资源的hook
export const useModalVideos = () => {
  return useVideoResources(VIDEOS, { autoLoad: false });
};

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  onPress?: () => void;
  children?: React.ReactNode;
  coins?: number;
  type?: 'withAd' | 'normal';
  upgradeCoins?: number;
}

export default function Modal({
  onClose,
  onPress,
  coins = 0,
  upgradeCoins = 0,
}: ModalProps) {
  const theme = useAtomValue(themeAtom);
  const styles = getStyles(theme === 'dark' ? 'dark' : 'light');
  const [playState, setPlayState] = useState<VideoPlayState>(VideoPlayState.BACKGROUND_START_AND_CONFETTI);
  const [showContent, setShowContent] = useState(false);
  const [backgroundLoopVisible, setBackgroundLoopVisible] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [modalInfo, setModalInfo] = useAtom(showRewardModalAtom)
  const rewardGoldCoin = useRewardGoldCoin()

  // 添加一个定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  // 背景视频开始加载的时间
  const bgStartLoadTimeRef = useRef<number>(0);

  // 创建动画值
  const scaleAnim = useRef(new Animated.Value(0)).current;

  // 使用 useVideoResources hook 加载视频资源
  const { paths } = useVideoResources(VIDEOS, {
    autoLoad: modalInfo.showRewardModal, // 当弹窗显示时自动加载资源
  });

  // 背景开始视频加载完成回调
  const handleBgStartLoad = useCallback(() => {
    // 记录背景视频开始加载的时间
    bgStartLoadTimeRef.current = Date.now();

    // 计算预加载时间
    const preloadDelay = Math.max(BACKGROUND_START_DURATION - PRELOAD_LOOP_TIME, 0);

    // 清除可能存在的旧定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 设置新的定时器，提前预加载BACKGROUND_LOOP
    timerRef.current = setTimeout(() => {
      setBackgroundLoopVisible(true);
    }, preloadDelay);
  }, []);

  // 背景视频播放完成回调
  const handleBgVideoComplete = useCallback(() => {
    // 如果还没有显示背景循环视频，立即显示
    if (!backgroundLoopVisible) {
      setBackgroundLoopVisible(true);
    }

    // 清除定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, [backgroundLoopVisible]);

  // 初始动画已经不需要，直接在visible变化时触发动画
  useEffect(() => {
    if (modalInfo.showRewardModal) {
      // 显示内容并启动缩放动画
      setShowContent(true);
      setShowConfetti(true);

      // 启动缩放动画
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        easing: customEasing,
        useNativeDriver: true
      }).start();
    }
  }, [modalInfo.showRewardModal, scaleAnim]);

  // 当弹窗关闭时重置状态和清除定时器
  useEffect(() => {
    if (!modalInfo.showRewardModal) {
      setPlayState(VideoPlayState.BACKGROUND_START_AND_CONFETTI);
      setShowContent(false);
      setShowConfetti(false);
      scaleAnim.setValue(0);
      setBackgroundLoopVisible(false);
      bgStartLoadTimeRef.current = 0;

      // 清除定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    }
  }, [modalInfo.showRewardModal, scaleAnim]);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // 渲染视频内容
  const renderVideo = () => {
    switch (playState) {
      case VideoPlayState.BACKGROUND_START_AND_CONFETTI:
        return (
          <>
            {showConfetti && (
              <ConfettiAnimation
                autoPlay
                loop={false}
              />
            )}
          </>
        );

      default:
        return null;
    }
  };

  if (!modalInfo.showRewardModal) return null;

  return (
    // <RNModal
    //   visible={visible}
    //   transparent={true}
    //   animationType="fade"
    //   onRequestClose={onClose}
    //   statusBarTranslucent={true}
    // >
    <View style={styles.container}>
      <View style={styles.modalContainer}>
        {/* 关闭按钮 */}
        <TouchableWithoutFeedback onPress={() => {
          setModalInfo({
            showRewardModal: false,
            coins: 0,
            upgradeCoins: 0
          })
          onClose()
        }}>
          <View style={styles.closeButton}>
            <CloseIcon size={16} color="#FFFFFF" />
          </View>
        </TouchableWithoutFeedback>

        {/* 渲染视频内容 */}
        {renderVideo()}

        {/* 显示内容 */}
        {showContent ? modalInfo.modalType === 'withAd' ? (<AdRewardModalContent
          btnText='看广告额外获得'
          coins={modalInfo.coins as number}
          upgradeCoins={modalInfo.upgradeCoins as number}
          onClose={() => {
            setModalInfo({
              showRewardModal: false,
              coins: 0,
              upgradeCoins: 0
            })
            onClose()
          }}
          onPress={async () => {
            onPress ? onPress() : onClose()
            try {
              setModalInfo({
                showRewardModal: false
              })
              const res = await watchAd({
                sourceName: AD_SOURCE.LISTEN_TASK,
                positionName: LISTEN_TASK_POSITION.positionName,
                slotId: LISTEN_TASK_POSITION.slotId,
                rewardType: RewardType.LISTEN_TASK,
                coins: upgradeCoins,
                rewardVideoStyle: 0,
                configPositionName: 'inspire_video_listen_book_rn',
                extInfo: JSON.stringify({sourceName: AD_SOURCE.LISTEN_TASK,})
              });
              if(res.success) {
                // 获取当前听书时长
                let currentListenDuration = 0;
                let encryptedListenDuration = '';
                try {
                  const listenTimeRes = await NativeModules.ListenTime?.getListenDuration();
                  currentListenDuration = listenTimeRes?.listenDuration || 0;
                  console.info('debug_extraReward_listenDuration', '当前听书时长', currentListenDuration);

                  // 使用 AES 加密听书时长
                  encryptedListenDuration = encryptLocalDuration(currentListenDuration);
                  console.info('debug_extraReward_encrypted_duration', '加密后的听书时长', encryptedListenDuration);
                } catch (error) {
                  console.warn('debug_extraReward_listenDuration_error', '获取或加密听书时长失败', error);
                }

                const result = await rewardGoldCoin({
                  rewardType: RewardType.EXTRA_REWARDS,
                  sourceName: AD_SOURCE.LISTEN_TASK,
                  coins: upgradeCoins, //  自己传
                  adId: res.adId, //  广告给
                  adResponseId: res.adResponseId, // 广告给
                  encryptType: res.encryptType, // 广告给
                  ecpm: res.ecpm, // 广告给
                  fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL, //广告给
                  extMap: JSON.stringify({
                    source: AD_SOURCE.LISTEN_TASK,
                    listenDuration: encryptedListenDuration
                  })
                }, true)
                if(result?.success) {
                  setModalInfo({
                    showRewardModal: true,
                    modalType: 'normal',
                    coins: result?.coins,
                    upgradeCoins: 0
                  })
                }  else {
                  Toast.info('获取奖励失败')
                }
              }
            } catch (e) {
              Toast.info('获取奖励失败')
            }
          }}
          scaleAnim={scaleAnim}
        />
        ) : (
          <RewardModalContent
            coins={modalInfo.coins || 0}
            // title={modalInfo.title}
            // subTitle={modalInfo.subTitle}
            onPress={() => {
              setModalInfo({ showRewardModal: false })
              onClose()
            }}
            scaleAnim={scaleAnim}
            btnText='开心收下'
          />
        ) : null}
      </View>
    </View>
    // </RNModal>
  );
} 