# ListenTask 组件 - WelfareLifecycle 监听功能

## 功能概述

本组件实现了监听客户端生命周期模块的 `onResume` 回调功能，当回调触发时会重新请求收听任务的接口数据并刷新收听时长模块的视图。

支持两种生命周期监听方式：
1. **WelfareLifecycle** - 福利模块专用的生命周期监听器（优先使用）
2. **PageEventEmitter** - 通用的页面事件监听器（备用方案）

## 实现方案

### 1. 架构设计

- **Store 层**：在 `store.ts` 中管理 WelfareLifecycle 事件监听器
- **组件层**：在 `index.tsx` 中使用 store 提供的 atom 来初始化和清理监听器

### 2. 核心功能

#### 2.1 监听器管理 (store.ts)

```typescript
// 初始化 WelfareLifecycle 监听器
export const initWelfareLifecycleListenerAtom = atom(
  null,
  async (_, set) => {
    // 检查模块可用性
    // 设置 onResume 事件监听
    // 在回调中重新获取收听任务数据
  }
);

// 清理监听器
export const cleanupWelfareLifecycleListenerAtom = atom(
  null,
  () => {
    // 移除事件监听器
  }
);
```

#### 2.2 组件集成 (index.tsx)

```typescript
export const ListenTask: React.FC = () => {
  const fetchListenTask = useSetAtom(writeListenTaskAtom)
  const initWelfareLifecycleListener = useSetAtom(initWelfareLifecycleListenerAtom)
  const cleanupWelfareLifecycleListener = useSetAtom(cleanupWelfareLifecycleListenerAtom)

  useEffect(() => {
    fetchListenTask()
    initWelfareLifecycleListener()

    return () => {
      cleanupWelfareLifecycleListener()
    };
  }, [])
  
  // ...
}
```

### 3. 事件流程

1. **组件初始化**：
   - 获取初始收听任务数据
   - 初始化 WelfareLifecycle 监听器

2. **onResume 回调触发**：
   - 客户端触发 WelfareLifecycle 的 onResume 事件
   - 监听器接收到事件
   - 重新调用 `writeListenTaskAtom` 获取最新数据
   - 组件视图自动更新

3. **组件卸载**：
   - 清理 WelfareLifecycle 监听器
   - 释放资源

### 4. 调试日志

实现中添加了详细的调试日志，便于问题排查：

- `debug_lifecycle_check`: 检查可用的生命周期模块
- `debug_lifecycle_init`: 监听器初始化
- `debug_lifecycle_onResume`: onResume 回调触发
- `debug_lifecycle_cleanup`: 监听器清理
- `debug_lifecycle_error`: 错误信息
- `debug_lifecycle_unavailable`: 模块不可用警告

### 5. 错误处理

- 检查 WelfareLifecycle 和 PageEventEmitter 模块可用性
- 自动降级：WelfareLifecycle 不可用时使用 PageEventEmitter
- 捕获监听器初始化异常
- 防止重复初始化监听器
- 确保组件卸载时正确清理资源

## 使用说明

1. **前置条件**：
   - 客户端需要提供 `WelfareLifecycle` 或 `Page` 原生模块之一
   - 模块需要支持 `onResume` 事件

2. **自动工作**：
   - 组件加载时自动初始化监听
   - 无需手动调用任何方法
   - 组件卸载时自动清理

3. **监控方式**：
   - 查看控制台日志确认监听器状态
   - 观察收听任务数据是否在 onResume 时更新

## 注意事项

- 监听器使用单例模式，避免重复创建
- 组件卸载时必须清理监听器，防止内存泄漏
- 自动降级机制：优先使用 WelfareLifecycle，不可用时自动使用 PageEventEmitter
- 如果所有生命周期模块都不可用，会输出警告但不影响其他功能

## 模块可用性检查

### 检查方式

组件初始化时会自动检查可用的生命周期模块：

```typescript
console.info('debug_lifecycle_check', '检查生命周期模块', {
  WelfareLifecycle: !!WelfareLifecycle,
  PageEventEmitter: !!PageEventEmitter,
  availableModules: Object.keys(NativeModules).filter(key =>
    key.includes('Welfare') || key.includes('Lifecycle') || key.includes('Page')
  )
});
```

### 降级策略

1. **优先使用 WelfareLifecycle**：如果可用，使用专用的福利模块生命周期监听器
2. **备用 PageEventEmitter**：如果 WelfareLifecycle 不可用，使用通用的页面事件监听器
3. **完全不可用**：如果都不可用，输出警告并跳过监听器设置

## extMap 参数增强

### 功能说明

在 `rewardGoldCoin` 接口调用中，我们在 `extMap` 参数中添加了 `listenDuration` 字段，用于传递当前用户的听书时长信息。

### 实现方式

1. **获取听书时长**：
   ```typescript
   let currentListenDuration = 0;
   try {
     const listenTimeRes = await NativeModules.ListenTime?.getListenDuration();
     currentListenDuration = listenTimeRes?.listenDuration || 0;
   } catch (error) {
     console.warn('获取听书时长失败', error);
   }
   ```

2. **AES 加密听书时长**：
   ```typescript
   let encryptedListenDuration = '';
   try {
     // 使用 AES 加密听书时长
     encryptedListenDuration = encryptLocalDuration(currentListenDuration);
     console.info('加密后的听书时长', encryptedListenDuration);
   } catch (error) {
     console.warn('加密听书时长失败', error);
   }
   ```

3. **构造 extMap 参数**：
   ```typescript
   extMap: JSON.stringify({
     sourceName: AD_SOURCE.LISTEN_TASK,
     listenDuration: encryptedListenDuration  // 使用加密后的值
   })
   ```

### 涉及的文件

- `src/components/CoinCenter/ListenTask/index.tsx` - 主要奖励领取
- `src/components/CoinCenter/ListenTask/ListenTaskModal/index.tsx` - 额外奖励领取

### 数据格式

发送到服务端的 `extMap` 数据格式：
```json
{
  "sourceName": "VIDEO_TASK",
  "listenDuration": "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIK+jbf6pdCQ="
}
```

### AES 加密详情

- **加密算法**：AES-256-ECB
- **密钥**：`1tyt1zuKMloXu/prwDTm5Q==` (Base64 编码)
- **加密模式**：ECB (Electronic Codebook)
- **填充方式**：PKCS7 (等同于 PKCS5)
- **输出格式**：Base64 字符串

**与 JAVA 代码对应关系**：
- JAVA: `AES/ECB/PKCS5Padding` ↔ JS: `AES.encrypt(text, key, { mode: ECB, padding: PKCS7 })`
- JAVA: `Base64.decode("1tyt1zuKMloXu/prwDTm5Q==")` ↔ JS: `CryptoJS.enc.Base64.parse('1tyt1zuKMloXu/prwDTm5Q==')`
- JAVA: `String.valueOf(price).getBytes(UTF_8)` ↔ JS: `duration.toString()`

**加密流程**：
1. 获取原始听书时长（秒）
2. 转换为字符串 (对应 JAVA 的 `String.valueOf(price)`)
3. 使用 AES-ECB 模式加密 (对应 JAVA 的 `AES/ECB/PKCS5Padding`)
4. 返回 Base64 编码的密文

**原始数据示例**：`1800` (秒) → **加密后**：`[Base64 encoded string]`

### 调试和验证

可以通过以下日志来验证加密是否正常工作：

- `debug_claimReward_listenDuration`: 显示原始听书时长
- `debug_claimReward_encrypted_duration`: 显示加密后的听书时长
- `debug_extraReward_listenDuration`: 额外奖励的原始听书时长
- `debug_extraReward_encrypted_duration`: 额外奖励的加密后听书时长

### 错误处理

- 如果获取听书时长失败，使用默认值 0
- 如果 AES 加密失败，返回空字符串
- 所有错误都会记录到控制台，便于调试

### 测试和验证

#### AES 加密测试

在开发环境中，组件初始化时会自动运行 AES 加密测试：

```typescript
// 测试函数会输出类似以下的日志
=== AES 加密测试 ===
原始值: 0 → 加密后: [Base64 string]
原始值: 100 → 加密后: [Base64 string]
原始值: 1800 → 加密后: [Base64 string]
原始值: 3600 → 加密后: [Base64 string]
原始值: 7200 → 加密后: [Base64 string]
=== 测试完成 ===
```

#### 与 JAVA 代码对比验证

可以使用相同的测试数据在 JAVA 端运行 `encodePrice()` 方法，对比加密结果是否一致：

```java
// JAVA 测试代码
public static void testEncryption() {
    int[] testValues = {0, 100, 1800, 3600, 7200};
    for (int value : testValues) {
        String encrypted = encodePrice(value);
        System.out.println("原始值: " + value + " → 加密后: " + encrypted);
    }
}
```

如果 JS 和 JAVA 的加密结果一致，说明实现正确。
