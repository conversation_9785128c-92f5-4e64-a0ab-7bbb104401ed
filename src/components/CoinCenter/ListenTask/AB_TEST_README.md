# ListenTask 模块 AB 测试控制

## 功能概述

ListenTask 模块现在支持通过 AB 测试配置来控制是否显示。只有当 `touchToWelfareEnable` 条件成立时，ListenTask 模块才会被加载和显示。

## 实现方案

### 1. AB 测试检查逻辑

使用 `getTouchToWelfareEnableABTest` 工具函数来检查 AB 测试配置：

```typescript
import { getTouchToWelfareEnableABTest } from "utilsV2/getTouchToWelfareEnableABTest";

const shouldShow = await getTouchToWelfareEnableABTest();
```

### 2. 检查逻辑

1. **使用 ADABTest 模块**：
   ```typescript
   NativeModules.ADABTest.touchToWelfareEnable
   ```

2. **默认行为**：
   - 如果 ADABTest 模块不可用，默认显示 ListenTask
   - 如果检查过程中出现错误，默认显示 ListenTask

### 3. 条件渲染

在 CoinCenter 页面中，ListenTask 组件和 ListenTaskModal 都受到 AB 测试控制：

```typescript
// ListenTask 组件
{shouldShowListenTask && <ListenTask />}

// ListenTaskModal 组件
{shouldShowListenTask && <ListenTaskModal visible={true} onClose={() => {}} type="withAd" />}
```

## 调试信息

### 调试日志

实现中添加了详细的调试日志：

- `debug_touch_welfare_abtest_modules`: 显示可用的 AB 测试模块
- `debug_touch_welfare_abtest`: 显示使用的具体 AB 测试方法和结果
- `debug_touch_welfare_abtest_unavailable`: AB 测试模块不可用警告
- `debug_touch_welfare_abtest_error`: AB 测试检查错误
- `debug_listen_task_abtest`: ListenTask 模块最终显示状态

### 示例日志输出

```javascript
// 模块检查
debug_touch_welfare_abtest_modules 检查 ADABTest 模块 {
  ADABTest: true,
  touchToWelfareEnable: true,
  allABModules: ["ADABTest"]
}

// AB 测试结果
debug_touch_welfare_abtest 使用 ADABTest.touchToWelfareEnable {
  value: true,
  type: "boolean",
  result: true
}

// 最终状态
debug_listen_task_abtest ListenTask 模块显示状态 true
```

## 使用说明

### 1. 客户端配置

确保客户端提供 ADABTest 模块：

- **ADABTest 模块**：包含 `touchToWelfareEnable` 属性

### 2. AB 测试配置

- **启用 ListenTask**：设置 `touchToWelfareEnable = true`
- **禁用 ListenTask**：设置 `touchToWelfareEnable = false`

### 3. 自动工作

- 页面加载时自动检查 AB 测试配置
- 根据配置结果决定是否显示 ListenTask 相关组件
- 无需手动调用任何方法

## 错误处理

- **模块不可用**：默认显示 ListenTask，确保功能可用性
- **检查异常**：捕获错误并默认显示 ListenTask
- **配置缺失**：使用默认值 `true`（默认显示 ListenTask）

## 注意事项

1. **向后兼容**：如果 ADABTest 模块不可用，默认显示 ListenTask
2. **性能优化**：只在页面初始化时检查一次 AB 测试配置
3. **调试友好**：提供详细的日志信息便于问题排查
4. **容错机制**：错误时降级策略确保功能稳定性
