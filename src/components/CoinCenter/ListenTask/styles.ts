import { StyleSheet, Platform } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

export const getStyles = (theme: any) => StyleSheet.create({
  container: {
    marginTop: px(20),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: px(22),
    paddingHorizontal: px(16),
  },
  title: {
    fontSize: px(14),
    // fontWeight: '600',
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    color: theme.titleColor,
    marginBottom: px(4),
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  
  limitText: {
    fontSize: px(11),
    color: '#FF4444',
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    //背景
    backgroundColor: 'rgba(255, 68, 68, 0.1)', // #FF4444 10%透明度
    paddingHorizontal: px(3),
    paddingVertical: px(1),
    marginLeft: px(6),
    borderRadius: px(2),
  },
  totalCoins: {
    marginTop:px(2),
    fontSize: px(12),
    color: '#999999',
  },
  textStyle: {
    fontSize: px(11),
    color: theme.tipsColor,
    opacity: 0.3
  },
  progressContainer: {
    marginBottom: 24,
  },
  progressDaysScroll: {
    
    // paddingHorizontal: 16,
    // paddingBottom: 20,
  },
  stepTag: {
    paddingHorizontal: px(5),
    marginBottom: 8,
    display: 'flex',
    flexDirection:'column',
    alignItems: 'center',
  },
  atMostIcon: {
    width: px(14),
    height: px(14),
    marginLeft: px(1),
    marginRight: px(2)
  },
  claimedIcon: {
    width: px(14),
    height: px(14),
    marginLeft: px(1),
    marginRight: px(2),
    opacity: theme.claimedOpacity
  },
  triangle: {
    width: 0,
    height: 0,
    marginTop: -1,
    borderLeftWidth: 10,
    borderLeftColor: 'transparent',
    borderRightWidth: 10,
    borderRightColor: 'transparent',
    borderTopWidth: 5,
    borderTopColor: theme.coinBubbleColor
  },
  trianglePendding: {
    borderTopColor: '#ff4444'
  },
  triangleCompleted: {
    borderTopColor: theme.tagColor,
  },
  stepItem: {
    alignItems: 'center',
  },
  stepProgress: {
    width: '100%',
    height: px(6),
    display: 'flex',
    position: 'relative',
  },
  stepLine: {
    width: '101%',
    height: px(2),
    position: 'absolute',
    top: '50%',
    marginTop: -px(1),
    // backgroundColor: theme.dotColor || '#E5E5E5',
    // backgroundColor: theme.dotColor || '#E5E5E5',
  },
  currentStep: {
    opacity: 1,
  },
  completedStep: {
    opacity: 0.7,
  },
  completedColor: {
    backgroundColor: theme.tagColor,
  },
  stepInfo: {
    alignItems: 'center',
    marginTop: px(8)
  },
  stepTime: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 8,
  },
  stepFinishTime: {
    color: '#ff4444'
  },
  stepAmount: {
    fontSize: 14,
    color: '#FF6B6B',
  },
  stepDot: {
    width: px(6),
    height: px(6),
    borderRadius: px(3),
    backgroundColor: theme.dotColor || '#E5E5E5',
    alignSelf: 'center',
    position: 'absolute',
    zIndex: 1000
  },
  stepDotUnfinished: {
    backgroundColor: theme.unfinishedDot
  },
  currentDot: {
    backgroundColor: theme.currentDotColor || '#FF4B4B',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  completedDot: {
    backgroundColor: theme.completedDotColor || '#FFB6B6',
  },
  rewardContainer: {
    padding: 16,
    backgroundColor: theme.rewardBgColor || '#FFF5F5',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  coinIcon: {
    width: 40,
    height: 40,
    marginRight: 12,
  },
  rewardInfo: {
    flex: 1,
  },
  description: {
    fontSize: 14,
    color: theme.descColor,
    marginBottom: 12,
  },
  button: {
    backgroundColor: theme.buttonBgColor || '#F5F5F5',
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderRadius: 20,
    alignItems: 'center',
  },
  activeButton: {
    backgroundColor: theme.activeButtonBgColor || '#FF4B4B',
  },
  disabledButton: {
    backgroundColor: theme.disabledButtonBgColor || '#F5F5F5',
    opacity: 0.7,
  },
  buttonText: {
    fontSize: 14,
    color: theme.buttonTextColor || '#999',
  },
  activeButtonText: {
    color: theme.activeButtonTextColor || '#FFFFFF',
  },
  coinBubble: {
    paddingHorizontal: px(8),
    // paddingVertical: px(6),
    height: px(28),
    borderRadius: px(9),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.coinBubbleColor || '#FF4B4B',
  },
  coinBubblePendding: {
    backgroundColor: '#FF4444'
  },
  coinAmount: {
    color: theme.amountColor,
    fontSize: 14,
    fontWeight: '600',
  },
  coinAmountUnfinished: {
    color: theme.unfinishedAmountColor
  },
  coinAmountClaimed: {
    opacity: theme.claimedOpacity,
  },
  xmNumber: {
    fontFamily: 'XmlyNumber',
  }
});