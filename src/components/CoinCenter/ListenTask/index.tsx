import React, { useEffect, useRef, useState } from 'react'
import { View, Text, TouchableOpacity, ScrollView, Image } from 'react-native'
import Animated<PERSON><PERSON>ieView from 'lottie-react-native'
import { NativeModules } from 'react-native'
import { useFocusEffect } from '@react-navigation/native'
import LinearGradient from 'react-native-linear-gradient'
import { Toast, Page } from '@xmly/rn-sdk'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { useAtomValue, useSetAtom } from 'jotai'
import { FallbackReqType } from 'constants/ad'
import { px } from 'utils/px'
import { listenTaskAtom, writeListenTaskAtom } from './store'
import { showRewardModalAtom } from 'atom/listenTaskModal'
import watchAd, { WatchAdResult } from 'utils/watchAd'
import { LISTEN_TASK_POSITION, AD_SOURCE, RewardType } from 'constants/ad'
import { TaskStatus } from 'services/welfare/listenTask'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import xmlog from "utilsV2/xmlog";
import ModuleCard from '../common/ModuleCard'
import TaskButton from '../common/TaskButton'
import CoinAnimation from './CoinAnimation'
import listenTaskThemeAtom from './theme'
import { getStyles } from './styles'
import getXMRequestId from 'utilsV2/getXMRequestId'
import GlobalEventEmitter from 'utilsV2/globalEventEmitter'

const atMostIcon = 'https://imagev2.xmcdn.com/storages/a7d9-audiofreehighqps/22/96/GKwRIRwLwqBZAAAJPgONZwd5.png'
const finishedIcon = 'https://imagev2.xmcdn.com/storages/220a-audiofreehighqps/4E/72/GAqh_aQL19fZAAABDAOZ9DHC.png'

// 引导动画 lottie 源数据（参考大转盘的动画）
const GUIDE_HAND_LOTTIE = {"v":"5.6.3","fr":25,"ip":9,"op":113,"w":162,"h":162,"nm":"容器 10080@3x","ddd":0,"assets":[{"id":"image_0","w":96,"h":81,"u":"","p":"data:image/png;base64,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","e":1}],"layers":[{"ddd":0,"ind":1,"ty":2,"nm":"容器 <EMAIL>","cl":"png","refId":"image_0","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[0]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":9,"s":[100]},{"i":{"x":[0.833],"y":[0.167]},"o":{"x":[0.167],"y":[0.167]},"t":113,"s":[100]},{"t":126,"s":[0]}],"ix":11},"r":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":9,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":21,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":33,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":46,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":59,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":72,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":86,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.167],"y":[0]},"t":99,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.167],"y":[0]},"t":113,"s":[0]},{"t":126,"s":[0]}],"ix":10},"p":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":9,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":21,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":33,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":46,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":59,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":72,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":86,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.167,"y":0},"t":99,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":0.667},"o":{"x":0.167,"y":0.167},"t":113,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"t":126,"s":[107,112,0]}],"ix":2},"a":{"a":0,"k":[48,40.5,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"ip":0,"op":250,"st":0,"bm":0}],"markers":[]}

export const ListenTask: React.FC = () => {
  const listenTaskInfo = useAtomValue(listenTaskAtom)
  const fetchListenTask = useSetAtom(writeListenTaskAtom)
  const theme = useAtomValue(listenTaskThemeAtom)
  const rewardGoldCoin = useRewardGoldCoin()
  const scrollRef = useRef<ScrollView>(null)
  const [lastMarginRight, setLastMarginRight] = useState(10)
  const setModalInfo = useSetAtom(showRewardModalAtom)
  const hasReportedExposure = useRef(false)
  const listenTaskRef = useRef<View>(null)
  
  // 引导动画相关状态
  const [showListenTaskGuide, setShowListenTaskGuide] = useState(false)
  const buttonRef = useRef<View>(null)
  const [guideRect, setGuideRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null)

  useEffect(() => {
    fetchListenTask()
  }, [])

  // 监听定位事件
  useEffect(() => {
    const locateListener = GlobalEventEmitter.addListener('locateListenTask', () => {
      // 当收到定位事件时，触发听书模块的定位
      if (listenTaskRef.current) {
        listenTaskRef.current.measure((x, y, width, height, pageX, pageY) => {
          // 触发页面滚动到听书模块位置
          GlobalEventEmitter.emit('scrollToListenTask', { y: pageY });
        });
      }
    });

    return () => {
      locateListener?.remove();
    };
  }, []);

  // 监听显示引导动画事件
  useEffect(() => {
    const showGuideListener = GlobalEventEmitter.addListener('showListenTaskGuide', () => {
      console.log('收到显示引导动画事件')
      // 延迟显示引导动画，等待页面滚动完成，然后测量按钮位置
      const tryMeasure = () => {
        const parent = listenTaskRef.current as any
        const btn = buttonRef.current as any
        console.log('尝试测量按钮位置:', { parent: !!parent, btn: !!btn, hasMeasureLayout: btn && typeof btn.measureLayout === 'function' })
        
        if (parent && btn && typeof btn.measureLayout === 'function') {
          btn.measureLayout(
            parent,
            (x: number, y: number, width: number, height: number) => {
              console.log('按钮位置测量成功:', { x, y, width, height })
              setGuideRect({ x, y, width, height })
              setShowListenTaskGuide(true)
            },
            () => {
              console.log('按钮位置测量失败，重试')
              setTimeout(tryMeasure, 200)
            }
          )
        } else {
          console.log('按钮或父组件未准备好，重试')
          setTimeout(tryMeasure, 200)
        }
      }
      setTimeout(tryMeasure, 600)
    });

    return () => {
      showGuideListener?.remove();
    };
  }, []);

  // 听书任务曝光埋点上报
  useEffect(() => {
    const reportExposure = async () => {
      if (listenTaskInfo.success && listenTaskInfo.title) {
        try {
          const xmRequestId = await getXMRequestId()
          const isDuplicateView = hasReportedExposure.current ? '1' : '0'
          
          // 福利中心-收听任务  控件曝光
          xmlog.event(68288, 'slipPage', {
            currPage: 'welfareCenter',
            xmRequestId: xmRequestId || '',
            isDuplicateView: isDuplicateView,
            taskTitle: listenTaskInfo.btnText || (listenTaskInfo.status === TaskStatus.PENDING ? '去领取' : '去收听'),
            taskId: 'listen_task' // 使用固定的听书任务ID
          })
          hasReportedExposure.current = true
        } catch (error) {
          console.warn('听书任务模块曝光埋点上报失败:', error)
        }
      }
    }

    reportExposure()
  }, [listenTaskInfo.success, listenTaskInfo.btnText, listenTaskInfo.status])

  const styles = getStyles(theme)

  const onShow = () => {
    // 曝光埋点已在 useEffect 中实现
  }

  function handleLayout(e: any) {
    console.log('handleLayout', e.nativeEvent.layout.width)
    const scrollWidth = e.nativeEvent.layout.width
    setLastMarginRight(Math.max(10, (scrollWidth - 60 * 6) / 6))
  }

  const claimReward = async (amount?: number) => {
    try {
      // 隐藏引导动画
      setShowListenTaskGuide(false)
      clickReport(amount ? `${amount}` : '去领取')
      var res: WatchAdResult;
      if (listenTaskInfo.activityInfo && listenTaskInfo.activityInfo !== '') {
        //限时任务，不用看广告，可以直接领取
        res = {
          success: true,
          adId: -9999,
          adResponseId: -9999,
          encryptType: '-9999',
          ecpm: '-9999',
          fallbackReq: FallbackReqType.NORMAL
        }
      } else {
        //原来任务
        res = await watchAd({
          sourceName: AD_SOURCE.LISTEN_TASK,
          positionName: LISTEN_TASK_POSITION.positionName,
          slotId: LISTEN_TASK_POSITION.slotId,
          rewardType: RewardType.LISTEN_TASK,
          coins: amount,
          rewardVideoStyle: 0,
          configPositionName: 'inspire_video_listen_book_rn',
          extInfo: JSON.stringify({ sourceName: AD_SOURCE.LISTEN_TASK, })
        })
      }
      if (res.success) {
        const result = await rewardGoldCoin(
          {
            rewardType: RewardType.LISTEN_TASK,
            sourceName: AD_SOURCE.LISTEN_TASK,
            coins: amount || 0, //  自己传
            adId: res.adId, //  广告给
            adResponseId: res.adResponseId, // 广告给
            encryptType: res.encryptType, // 广告给
            ecpm: res.ecpm, // 广告给
            fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL, //广告给
          },
          true
        )
        if (result?.success) {
          fetchListenTask()
          setModalInfo({
            showRewardModal: true,
            coins: result?.coins,
            upgradeCoins: result?.upgradeCoins,
            modalType: result?.upgradeCoins ? 'withAd' : 'normal',
          })
        } else {
          Toast.info('获取奖励失败')
        }
      }
    } catch (e) {
      Toast.info('获取奖励失败')
    }
  }

  const clickReport = (title: string) => {
    // 福利中心-听书任务  点击事件
    xmlog.click(68287, 'ListenTasl', {
      currPage: 'welfareCenter',
      moduleTitle: '听书任务',
      taskTitle: title,
      taskId: `1`,
      Item: title
    });
  }

  if (!listenTaskInfo.success) return null

  return (
    <ScrollAnalyticComp
      itemKey={'ListenTask'}
      onShow={onShow}
    >
      <View ref={listenTaskRef}>
        <ModuleCard style={styles.container}>
        <View>
          <View style={styles.header}>
            <View style={{ maxWidth: '80%' }}>
              <View style={styles.titleRow}>
                <Text style={styles.title}>听书赚金币</Text>
                {listenTaskInfo.activityInfo && (<Text style={styles.limitText}>{listenTaskInfo.activityInfo}</Text>)}
              </View>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                style={styles.totalCoins}
              >
                {listenTaskInfo.title}
              </Text>
            </View>
            {listenTaskInfo.status === TaskStatus.CLAIMED ? (
              <Text style={styles.textStyle}>明日再来</Text>
            ) : (
              <View ref={buttonRef} collapsable={false}>
                <TaskButton
                  text={
                    listenTaskInfo.status === TaskStatus.PENDING 
                      ? (listenTaskInfo.activityInfo && listenTaskInfo.activityInfo !== '' ? '直接领' : '去领取')
                      : '去收听'
                  }
                  onPress={async () => {
                    if (listenTaskInfo.status === TaskStatus.PENDING) {
                      claimReward()
                    } else {
                      try {
                        setShowListenTaskGuide(false)
                        clickReport('去收听')
                        const trackInfo = await NativeModules.RNAudioPlayer?.currentTrack()
                        if (trackInfo.id || trackInfo.dataId || trackInfo.trackId) {
                          Page.start(`iting://open?msg_type=11&track_id=${trackInfo.id || trackInfo.dataId || trackInfo.trackId}`)
                        } else {
                          Page.start('iting://open?msg_type=332&id=recommend')
                        }
                      } catch (e) {
                        Page.start('iting://open?msg_type=332&id=recommend')
                        console.warn('跳转播放页失败' + JSON.stringify(e))
                      }
                    }
                  }}
                />
              </View>
            )}
          </View>
          <ScrollView
            ref={scrollRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            bounces={false}
            scrollEventThrottle={16}
            onLayout={handleLayout}
          >
            {listenTaskInfo.stepInfo.map((step, index) => {
              const bgColor = step.status === TaskStatus.UNFINISHED ? theme.unfinishedDotColor : '#FF4444'
              const penddingIndex = listenTaskInfo.stepInfo.findIndex((item) => item.status === TaskStatus.PENDING)

              return (
                <View
                  key={index}
                  style={[
                    styles.stepItem,
                    index === listenTaskInfo.currentStep && styles.currentStep,
                    index === 0 && { marginLeft: px(16) },
                    index === listenTaskInfo?.stepInfo?.length - 1 && { marginRight: px(16) },
                    // step.status === 2 && styles.completedStep,
                  ]}
                >
                  <TouchableOpacity
                    onPress={() => {
                      if (step.status === TaskStatus.PENDING) {
                        claimReward(step.amount)
                      }
                    }}
                  >
                    <View style={[styles.stepTag]}>
                      <View
                        style={[styles.coinBubble, step.status === TaskStatus.CLAIMED && styles.completedColor, step.status === TaskStatus.PENDING && styles.coinBubblePendding]}
                      >
                        {(step.status === TaskStatus.UNFINISHED || (step.status === TaskStatus.PENDING && index !== penddingIndex)) && (
                          <Image
                            source={{ uri: atMostIcon }}
                            key={atMostIcon}
                            style={styles.atMostIcon}
                          />
                        )}
                        {step.status === TaskStatus.PENDING && index === penddingIndex && <CoinAnimation loop />}
                        {step.status === TaskStatus.CLAIMED && (
                          <Image
                            source={{ uri: finishedIcon }}
                            key={finishedIcon}
                            style={styles.claimedIcon}
                          />
                        )}
                        <Text
                          style={[
                            styles.coinAmount,
                            step.status === TaskStatus.UNFINISHED && styles.coinAmountUnfinished,
                            step.status === TaskStatus.CLAIMED && styles.coinAmountClaimed,
                            styles.xmNumber,
                          ]}
                        >
                          +{step.amount}
                        </Text>
                      </View>
                      <View
                        style={[styles.triangle, step.status === TaskStatus.CLAIMED && styles.triangleCompleted, step.status === TaskStatus.PENDING && styles.trianglePendding]}
                      />
                    </View>
                  </TouchableOpacity>

                  <View style={styles.stepProgress}>
                    <View style={[styles.stepDot, { backgroundColor: bgColor }]} />
                    {index === 0 ? (
                      <LinearGradient
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        colors={['transparent', 'transparent', bgColor, bgColor]}
                        locations={[0, 0.5, 0.5, 1]}
                        style={styles.stepLine}
                      />
                    ) : index === listenTaskInfo?.stepInfo?.length - 1 ? (
                      <LinearGradient
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        colors={[bgColor, bgColor, 'transparent', 'transparent']}
                        locations={[0, 0.5, 0.5, 1]}
                        style={styles.stepLine}
                      />
                    ) : (
                      <View
                        style={[
                          styles.stepLine,
                          {
                            backgroundColor: bgColor,
                          },
                        ]}
                      />
                    )}
                  </View>
                  <View style={styles.stepInfo}>
                    <Text style={[styles.stepTime, step.status === TaskStatus.PENDING && styles.stepFinishTime, styles.xmNumber]}>{step.condition}分钟</Text>
                  </View>
                </View>
              )
            })}
          </ScrollView>
        </View>
        </ModuleCard>
        
        {/* 听书任务引导动画（锚定按钮） */}
        {showListenTaskGuide && (
          <View
            style={{
              position: 'absolute',
              left: guideRect ? guideRect.x + guideRect.width - px(10) : px(200),
              top: guideRect ? guideRect.y + (guideRect.height - px(54)) / 2 : px(100),
              width: px(54),
              height: px(54),
              zIndex: 9999,
              backgroundColor: guideRect ? 'transparent' : 'red', // 调试用
            }}
            pointerEvents="none"
          >
            {guideRect ? (
              <AnimatedLottieView
                source={GUIDE_HAND_LOTTIE}
                style={{ width: '100%', height: '100%' }}
                autoPlay
                loop
                speed={1.0}
              />
            ) : (
              <Text style={{ color: 'white', fontSize: 12, textAlign: 'center' }}>等待位置</Text>
            )}
          </View>
        )}
        
        {/* 调试信息 */}
        <View style={{
          position: 'absolute',
          top: px(50),
          right: px(16),
          backgroundColor: 'blue',
          padding: px(8),
          zIndex: 10000,
        }}>
          <Text style={{ color: 'white', fontSize: 10 }}>
            状态: {showListenTaskGuide ? '显示' : '隐藏'}
          </Text>
          <Text style={{ color: 'white', fontSize: 10 }}>
            位置: {guideRect ? '已获取' : '未获取'}
          </Text>
        </View>
      </View>
    </ScrollAnalyticComp>
  )
}

export default ListenTask
