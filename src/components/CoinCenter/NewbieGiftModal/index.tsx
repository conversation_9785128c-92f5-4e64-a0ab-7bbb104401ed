import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { useSetAtom } from 'jotai';
import { closeModalAtom } from 'components/common/ModalManager/store';

interface Props {
  onShow?: (traceId: number) => boolean | Promise<boolean>;
}

const { width, height } = Dimensions.get('window');

const NewbieGiftModal: React.FC<Props> = ({ onShow }) => {
  const closeModal = useSetAtom(closeModalAtom);

  useEffect(() => {
    try {
      onShow && onShow(Date.now());
    } catch {}
  }, [onShow]);

  const handleClose = () => {
    // 关闭自身并继续队列
    closeModal('NewbieGiftModal', false, 'user_close');
  };

  return (
    <View style={styles.overlay}>
      <TouchableOpacity activeOpacity={1} style={styles.container} onPress={handleClose}>
        <View style={styles.header}>
          <Text style={styles.title}>新人大礼包文案</Text>
        </View>
        <View style={styles.content} />
        <View style={styles.footer}>
          <TouchableOpacity style={styles.button} onPress={handleClose}>
            <Text style={styles.buttonText}>知道了</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width,
    height,
    backgroundColor: 'rgba(0,0,0,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  container: {
    width,
    height,
    backgroundColor: '#FF2D2D', // 全屏红色
  },
  header: {
    paddingTop: 48,
    paddingHorizontal: 20,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 22,
    fontWeight: '700',
  },
  content: {
    flex: 1,
  },
  footer: {
    paddingBottom: 40,
    alignItems: 'center',
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 22,
    backgroundColor: '#FFFFFF',
  },
  buttonText: {
    color: '#FF2D2D',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NewbieGiftModal;


