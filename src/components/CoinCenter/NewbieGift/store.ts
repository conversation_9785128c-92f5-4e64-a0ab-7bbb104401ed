import { atom } from 'jotai';
import { queryNewUserTaskInfo, NewUserTaskInfo, NewUserTaskItem } from '../../../services/welfare/newUserTask';

export enum NewbieGiftStatus {
  AVAILABLE = 0, // 可领取
  CLAIMED = 1,   // 已领取
  EXPIRED = 2,   // 已过期
}

export interface NewbieGiftItem {
  id: string;
  name: string;
  description: string;
  amount: number;
  icon: string;
  type: 'coin' | 'coupon' | 'vip' | 'NEWCOMER_GIFT' | 'SIGN_IN' | 'LISTEN_TASK' | 'DAILY_TASK' | 'DRINK_WATER' | 'FLIP_CARD_TASK' | 'SPINNING_WHEEL' | 'EXCHANGE_TASK';
  btnText: string;
  status: NewbieGiftStatus;
  taskId: string;
}

export interface NewbieGiftInfo {
  success: boolean;
  status: NewbieGiftStatus;
  title: string;
  subTitle: string;
  gifts: NewbieGiftItem[];
  expireTime?: number; // 过期时间戳
  btnText: string;
  icon?: string;
}

// 新人礼包信息原子（初始状态为loading）
export const newbieGiftAtom = atom<NewbieGiftInfo>({
  success: false, // 初始为false，等待服务端数据
  status: NewbieGiftStatus.AVAILABLE,
  title: '新人大礼包',
  subTitle: '加载中...',
  gifts: [],
  btnText: '加载中',
  icon: undefined, // 初始化icon字段
});

// 转换服务端数据到本地数据格式的辅助函数
const transformServerDataToLocal = (serverData: NewUserTaskInfo): NewbieGiftInfo => {
  const gifts: NewbieGiftItem[] = serverData.list.map((item: NewUserTaskItem, index: number) => ({
    id: item.taskId || index.toString(),
    name: item.title,
    description: item.subTitle || '',
    amount: item.coins,
    icon: 'https://imagev2.xmcdn.com/storages/c00d-audiofreehighqps/3E/2E/GAqh1QQMFFVRAAAEvAO-jCTD.png', // 使用默认图标
    type: item.type === 'NEWCOMER_GIFT' ? 'NEWCOMER_GIFT' : 
          item.type === 'SIGN_IN' ? 'SIGN_IN' : 
          item.type === 'LISTEN_TASK' ? 'LISTEN_TASK' : 
          item.type === 'DAILY_TASK' ? 'DAILY_TASK' : 
          item.type === 'DRINK_WATER' ? 'DRINK_WATER' : 
          item.type === 'FLIP_CARD_TASK' ? 'FLIP_CARD_TASK' : 
          item.type === 'SPINNING_WHEEL' ? 'SPINNING_WHEEL' : 
          item.type === 'EXCHANGE_TASK' ? 'EXCHANGE_TASK' : 'coin' as const,
    btnText: item.btnText,
    status: mapServerStatusToLocal(item.status),
    taskId: item.taskId,
  }));

  // 计算整体状态：如果所有任务都是COMPLETED(0)则为CLAIMED，否则为AVAILABLE
  const overallStatus = gifts.every(gift => gift.status === NewbieGiftStatus.CLAIMED) 
    ? NewbieGiftStatus.CLAIMED 
    : NewbieGiftStatus.AVAILABLE;

  return {
    success: serverData.success,
    status: overallStatus,
    title: serverData.title,
    subTitle: serverData.subtitle,
    gifts,
    btnText: overallStatus === NewbieGiftStatus.CLAIMED ? '已领取' : '立即领取',
    icon: serverData.icon,
  };
};

// 将服务端状态映射到本地状态
const mapServerStatusToLocal = (serverStatus: number): NewbieGiftStatus => {
  switch (serverStatus) {
    case 0:  // 0: 所有任务已完成，需要置灰显示
      return NewbieGiftStatus.CLAIMED;
    case 1:  // 1: 任务最开始的未完成状态
    case 2:  // 2: 可完成任务状态
    case 3:  // 3: 可完成任务状态
    case 4:  // 4: 可完成任务状态
    default: // 其他状态：都视为可操作状态
      return NewbieGiftStatus.AVAILABLE;
  }
};

// 获取新人礼包信息的写入原子
export const writeNewbieGiftAtom = atom(
  null,
  async (get, set, params?: { init?: boolean }) => {
    try {
      const response = await queryNewUserTaskInfo();
      
      if (response?.ret === 0 && response.data) {
        const transformedData = transformServerDataToLocal(response.data);
        set(newbieGiftAtom, transformedData);
      } else {
        // 服务端返回错误，设置为失败状态
        set(newbieGiftAtom, {
          success: false,
          status: NewbieGiftStatus.EXPIRED,
          title: '新人大礼包',
          subTitle: '',
          gifts: [],
          btnText: '获取失败',
        });
      }
    } catch (error) {
      console.error('Failed to fetch newbie gift info:', error);
      set(newbieGiftAtom, {
        success: false,
        status: NewbieGiftStatus.EXPIRED,
        title: '新人大礼包',
        subTitle: '',
        gifts: [],
        btnText: '网络错误',
      });
    }
  }
);

// 领取新人礼包的写入原子
export const claimNewbieGiftAtom = atom(
  null,
  async (get, set) => {
    try {
      const currentGift = get(newbieGiftAtom);
      if (currentGift.status !== NewbieGiftStatus.AVAILABLE) {
        return { success: false, message: '礼包不可领取' };
      }

      // 模拟API调用领取礼包
      // 这里应该替换为真实的API调用
      
      // 更新状态为已领取
      set(newbieGiftAtom, {
        ...currentGift,
        status: NewbieGiftStatus.CLAIMED,
        btnText: '已领取',
      });

      return { success: true, message: '领取成功' };
    } catch (error) {
      console.error('Failed to claim newbie gift:', error);
      return { success: false, message: '领取失败' };
    }
  }
);
