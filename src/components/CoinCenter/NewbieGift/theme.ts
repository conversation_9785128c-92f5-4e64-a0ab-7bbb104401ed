import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  headerBgColor: '#131313',
  tabColor: 'rgba(255, 255, 255, .5)',
  activeTabColor: '#FFFFFF',
  titleIcon: 'https://imagev2.xmcdn.com/storages/48f9-audiofreehighqps/5B/5B/GAqhqKwLsHO8AAAB5gOBpMOZ.png',
  countdownLabelColor: '#8D8D91',
  taskNameColor: '#FFFFFF',
  taskDescColor: '#8D8D91',
  headerBorderColor: '#000000',
  contentBgColor: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)', // 蓝色渐变背景
  titleColor: '#ffffff',
  tagColor: '#3a5998',
  color: '#FFFFFF',
  amountColor: '#FFFFFF',
  coinBubbleColor: '#4a6fa5',
  unfinishedAmountColor: '#ffffff',
  claimedOpacity: 0.1,
  tipsColor: '#ffffff',
  unfinishedDotColor: '#4a6fa5'
}

const lightTheme = {
  headerBgColor: '#FFFFFF',
  tabColor: 'rgba(36, 0, 0, .5)',
  activeTabColor: '#240000',
  titleIcon: 'https://imagev2.xmcdn.com/storages/ba4a-audiofreehighqps/90/97/GAqhQ6cLsHO8AAAB3gOBpMN9.png',
  countdownLabelColor: '#666666',
  taskNameColor: '#111111',
  taskDescColor: '#999999',
  headerBorderColor: '#F0F0F0',
  contentBgColor: 'linear-gradient(135deg, #4a90e2 0%, #357abd 100%)', // 蓝色渐变背景
  titleColor: '#ffffff',
  tagColor: '#E8F4FD',
  amountColor: '#ffffff',
  coinBubbleColor: '#5ba0f2',
  unfinishedAmountColor: '#ffffff',
  claimedOpacity: 1,
  tipsColor: '#ffffff',
  unfinishedDotColor: '#85C1E9'
}

const newbieGiftThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  console.log('-------------- newbieGift theme ------------->', theme)
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default newbieGiftThemeAtom;
