import { StyleSheet, Platform } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

export const getStyles = (theme: any) => StyleSheet.create({
  container: {
    marginTop: px(12),
    backgroundColor: '#FFFFFF',
    borderRadius: px(2),
    paddingVertical: px(16),
    // 去掉paddingHorizontal，让header可以贴边
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: px(49),
    marginBottom: px(20),
    // 去掉paddingHorizontal，让图片可以贴边
    marginTop: px(-16), // 抵消父容器的顶部padding
    borderTopLeftRadius: px(2),
    borderTopRightRadius: px(2),
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: px(16), // 给文字区域单独加左边距
  },
  title: {
    fontSize: px(15),
    fontWeight: Platform.select({ ios: '600', android: 'bold' }),
    color: '#DF6262',
    marginRight: px(8),
  },
  timeLimit: {
    fontSize: px(10),
    color: '#DD7171',
    backgroundColor: 'rgba(221, 113, 113, 0.1)',
    paddingHorizontal: px(8),
    paddingVertical: px(3),
    borderRadius: 0,
    alignSelf: 'flex-start',
  },
  giftBoxIcon: {
    width: px(60),
    height: px(60),
  },
  headerIcon: {
    height: px(49), // 明确设置高度
    width: px(60),  // 设置固定宽度，确保图片显示
    resizeMode: 'contain',
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    height: px(28), // 固定高度28px
    paddingHorizontal: px(16), // 为任务项添加左右padding
    marginBottom: px(20), // 任务间距20px
  },
  lastTaskItem: {
    marginBottom: 0, // 最后一个任务底部无边距
  },
  coinIcon: {
    width: px(20),
    height: px(20),
    marginRight: px(8),
  },
  taskInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskName: {
    fontSize: px(12),
    color: '#333333',
    fontWeight: Platform.select({ ios: '600', android: 'bold' }),
    marginRight: px(8), // 与金币数量的间距
  },
  coinAmount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: px(12),
  },
  coinAmountIcon: {
    width: px(12),
    height: px(12),
    marginRight: px(4),
  },
  coinAmountText: {
    fontSize: px(11),
    color: '#FF9500',
    fontWeight: Platform.select({ ios: '600', android: 'bold' }),
  },
  taskButton: {
    backgroundColor: '#FF4444',
    borderRadius: px(20),
    width: px(64),
    height: px(28),
    justifyContent: 'center',
    alignItems: 'center',
  },
  taskButtonText: {
    fontSize: px(11),
    color: '#FFFFFF',
    fontWeight: Platform.select({ ios: '500', android: 'normal' }),
    textAlign: 'center',
  },
  disabledButton: {
    backgroundColor: 'transparent',
  },
  disabledButtonText: {
    color: 'rgba(44, 44, 60, 0.3)',
  },
});
