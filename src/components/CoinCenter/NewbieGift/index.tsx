import React, { useEffect, useRef, useState } from 'react'
import { View, Text, TouchableOpacity, Image, Modal } from 'react-native'
import Animated<PERSON>ottieView from 'lottie-react-native'
import { useNavigation } from '@react-navigation/native'
import LinearGradient from 'react-native-linear-gradient'
import { Toast, Page } from '@xmly/rn-sdk'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { useAtomValue, useSetAtom } from 'jotai'

import { newbieGiftAtom, writeNewbieGiftAtom, claimNewbieGiftAtom, NewbieGiftStatus } from './store'
import newbieGiftThemeAtom from './theme'
import { getStyles } from './styles'
import { px } from 'utils/px'
import xmlog from "utilsV2/xmlog"
import getXMRequestId from 'utilsV2/getXMRequestId'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import { RewardType, AD_SOURCE, FallbackReqType } from 'constants/ad'
import RewardModalContent from '../RewardModalContent'
import signInNew from 'servicesV2/signInNew'
import useLocatedTask from 'hooksV2/useLocatedTask'
import GlobalEventEmitter from 'utilsV2/globalEventEmitter'

// ==================== 常量定义 ====================
const GIFT_BOX_ICON = 'https://imagev2.xmcdn.com/storages/220a-audiofreehighqps/4E/72/GAqh_aQL19fZAAABDAOZ9DHC.png'
const COIN_ICON = 'https://imagev2.xmcdn.com/storages/c00d-audiofreehighqps/3E/2E/GAqh1QQMFFVRAAAEvAO-jCTD.png'

// 引导动画 lottie 源数据（参考大转盘的动画）
const GUIDE_HAND_LOTTIE = {"v":"5.6.3","fr":25,"ip":9,"op":113,"w":162,"h":162,"nm":"容器 10080@3x","ddd":0,"assets":[{"id":"image_0","w":96,"h":81,"u":"","p":"data:image/png;base64,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","e":1}],"layers":[{"ddd":0,"ind":1,"ty":2,"nm":"容器 <EMAIL>","cl":"png","refId":"image_0","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[0]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":9,"s":[100]},{"i":{"x":[0.833],"y":[0.167]},"o":{"x":[0.167],"y":[0.167]},"t":113,"s":[100]},{"t":126,"s":[0]}],"ix":11},"r":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":9,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":21,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":33,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":46,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":59,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":72,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":86,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.167],"y":[0]},"t":99,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.167],"y":[0]},"t":113,"s":[0]},{"t":126,"s":[0]}],"ix":10},"p":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":9,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":21,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":33,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":46,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":59,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":72,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":86,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.167,"y":0},"t":99,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":0.667},"o":{"x":0.167,"y":0.167},"t":113,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"t":126,"s":[107,112,0]}],"ix":2},"a":{"a":0,"k":[48,40.5,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"ip":0,"op":250,"st":0,"bm":0}],"markers":[]}

// ==================== 组件定义 ====================
export const NewbieGift: React.FC = () => {
  // ==================== Hooks & State ====================
  const navigation = useNavigation()
  const newbieGiftInfo = useAtomValue(newbieGiftAtom)
  const fetchNewbieGift = useSetAtom(writeNewbieGiftAtom)
  const claimNewbieGift = useSetAtom(claimNewbieGiftAtom)
  const theme = useAtomValue(newbieGiftThemeAtom)
  const rewardGoldCoinHook = useRewardGoldCoin()
  
  const hasReportedExposure = useRef(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [rewardCoins, setRewardCoins] = useState(0)
  
  // 引导动画相关状态
  const [showListenTaskGuide, setShowListenTaskGuide] = useState(false)

  const styles = getStyles(theme)

  // ==================== 生命周期 ====================
  useEffect(() => {
    fetchNewbieGift({ init: true })
  }, [])

  // ==================== 埋点上报 ====================
  useEffect(() => {
    const reportExposure = async () => {
      if (newbieGiftInfo.success && newbieGiftInfo.title) {
        try {
          const xmRequestId = await getXMRequestId()
          const isDuplicateView = hasReportedExposure.current ? '1' : '0'
          
          // 福利中心-新人礼包 控件曝光
          xmlog.event(68289, 'slipPage', {
            currPage: 'welfareCenter',
            xmRequestId: xmRequestId || '',
            isDuplicateView: isDuplicateView,
            taskTitle: newbieGiftInfo.btnText,
            taskId: 'newbie_gift'
          })
          hasReportedExposure.current = true
        } catch (error) {
          console.warn('新人礼包模块曝光埋点上报失败:', error)
        }
      }
    }

    reportExposure()
  }, [newbieGiftInfo.success, newbieGiftInfo.btnText, newbieGiftInfo.status])

  // ==================== 引导动画控制（新人礼包内不再展示，仅在听书模块展示） ====================
  useEffect(() => {
    // 新人礼包内不显示引导动画
    setShowListenTaskGuide(false)
  }, [newbieGiftInfo.gifts])

  // ==================== 任务处理函数 ====================
  
  /**
   * 处理新人礼包任务
   */
  const handleNewcomerGiftTask = async (task: any) => {
    try {
      const result = await rewardGoldCoinHook({
        rewardType: RewardType.NEWCOMER_GIFT,
        sourceName: AD_SOURCE.NEWCOMER_GIFT,
        coins: task.amount,
        fallbackReq: FallbackReqType.NORMAL,
        extMap: JSON.stringify({
          taskType: 'NEWCOMER_GIFT',
          taskId: task.taskId || task.id
        })
      }, true)
      
      if (result?.success) {
        setRewardCoins(result.coins || task.amount)
        setModalVisible(true)
      } else {
        Toast.info(result?.toast || '领取失败')
      }
    } catch (error) {
      console.error('领金币失败:', error)
      Toast.info('领取失败，请稍后重试')
    }
  }

  /**
   * 处理签到任务
   */
  const handleSignInTask = async () => {
    try {
      const signInResult = await signInNew()
      if (signInResult?.ret === 0 && signInResult?.data?.code === 0) {
        Toast.success('签到成功！')
        fetchNewbieGift({ init: false })
      } else {
        Toast.info(signInResult?.data?.msg || signInResult?.msg || '签到失败')
      }
    } catch (error) {
      console.error('签到失败:', error)
      Toast.info('签到失败，请稍后重试')
    }
  }

  /**
   * 处理听书任务
   */
  const handleListenTask = (task: any) => {
    try {
      // 隐藏引导动画
      setShowListenTaskGuide(false)
      Toast.info('正在定位到听书模块...')
      // 触发定位事件
      GlobalEventEmitter.emit('locateListenTask')
      // 触发显示听书任务引导动画事件
      GlobalEventEmitter.emit('showListenTaskGuide')
    } catch (error) {
      console.error('定位听书模块失败:', error)
      Toast.info('定位失败，请稍后重试')
    }
  }

  /**
   * 处理翻卡任务
   */
  const handleFlipCardTask = (task: any) => {
    try {
      Toast.info('正在定位到翻卡任务...')
      GlobalEventEmitter.emit('locateFlipCardTask')
    } catch (error) {
      console.error('定位翻卡任务失败:', error)
      Toast.info('定位失败，请稍后重试')
    }
  }

  /**
   * 处理大转盘任务
   */
  const handleSpinningWheelTask = (task: any) => {
    try {
      Toast.info('正在定位到大转盘任务...')
      GlobalEventEmitter.emit('locateSpinningWheel')
    } catch (error) {
      console.error('定位大转盘任务失败:', error)
      Toast.info('定位失败，请稍后重试')
    }
  }

  /**
   * 处理换量任务
   */
  const handleExchangeTask = (task: any) => {
    try {
      Toast.info('正在跳转到收支记录...')
      navigation.navigate('CoinDetail')
    } catch (error) {
      console.error('跳转收支记录失败:', error)
      Toast.info('跳转失败，请稍后重试')
    }
  }



  /**
   * 处理喝水任务
   */
  const handleDrinkWaterTask = () => {
    try {
      navigation.navigate('DrinkWater')
    } catch (error) {
      console.error('跳转喝水页面失败:', error)
      Toast.info('跳转失败，请稍后重试')
    }
  }

  /**
   * 处理第一个任务（立即领取30000金币）
   */
  const handleFirstTask = async () => {
      const result = await claimNewbieGift()
      if (result.success) {
        Toast.success('领取成功！礼包已发放到您的账户')
      } else {
        Toast.info(result.message || '领取失败')
      }
  }

  /**
   * 处理第二个任务（去签到）
   */
  const handleSecondTask = () => {
    Page.start('iting://open?msg_type=sign_in')
  }

  /**
   * 处理第三个任务（提现到账）
   */
  const handleThirdTask = () => {
    Page.start('iting://open?msg_type=withdraw')
  }

  /**
   * 主任务处理函数
   */
  const handleTaskAction = async (task: any, index: number) => {
    const buttonText = getTaskButtonText(task, index)
    clickReport(task.name, buttonText)
    
    // 如果任务已完成，不执行任何操作
    if (task.status === NewbieGiftStatus.CLAIMED) {
      return
    }
    
    // 根据任务类型分发到对应的处理函数
    switch (task.type) {
      case 'NEWCOMER_GIFT':
        await handleNewcomerGiftTask(task)
        break
      case 'SIGN_IN':
        await handleSignInTask()
        break
      case 'LISTEN_TASK':
        handleListenTask(task)
        break
      case 'FLIP_CARD_TASK':
        handleFlipCardTask(task)
        break
      case 'SPINNING_WHEEL':
        handleSpinningWheelTask(task)
        break
      case 'EXCHANGE_TASK':
        handleExchangeTask(task)
        break
      case 'DRINK_WATER':
        handleDrinkWaterTask()
        break
      default:
        // 兼容旧的任务处理逻辑
        if (index === 0) {
          await handleFirstTask()
    } else if (index === 1) {
          handleSecondTask()
    } else if (index === 2) {
          handleThirdTask()
        }
        break
    }
  }

  // ==================== 工具函数 ====================
  
  /**
   * 获取任务按钮文字
   */
  const getTaskButtonText = (task: any, index: number) => {
    return task.btnText || '去完成'
  }

  /**
   * 判断任务是否已完成
   */
  const isTaskCompleted = (task: any, index: number) => {
    return task.status === NewbieGiftStatus.CLAIMED
  }

  /**
   * 处理弹窗关闭和数据刷新
   */
  const handleModalClose = () => {
    setModalVisible(false)
    fetchNewbieGift({ init: false })
  }

  /**
   * 点击埋点上报
   */
  const clickReport = (taskName: string, buttonText: string) => {
    xmlog.click(68290, 'NewbieGift', {
      currPage: 'welfareCenter',
      moduleTitle: '新人大礼包',
      taskTitle: taskName,
      taskId: '1',
      Item: buttonText
    })
  }

  // ==================== 渲染函数 ====================
  
  /**
   * 渲染任务项
   */
  const renderTaskItem = (task: any, index: number) => {
    const buttonText = getTaskButtonText(task, index)
    const isCompleted = isTaskCompleted(task, index)
    const isLast = index === newbieGiftInfo.gifts.length - 1

    return (
      <View key={task.id} style={[styles.taskItem, isLast && styles.lastTaskItem]}>
        <View style={styles.taskInfo}>
          <Text style={styles.taskName}>{task.name}</Text>
          
          <View style={styles.coinAmount}>
            <Image source={{ uri: COIN_ICON }} style={styles.coinAmountIcon} />
            <Text style={styles.coinAmountText}>+{task.amount}</Text>
          </View>
        </View>
        
        <TouchableOpacity 
          style={[styles.taskButton, isCompleted && styles.disabledButton]}
          onPress={() => handleTaskAction(task, index)}
          disabled={isCompleted}
        >
          <Text style={[styles.taskButtonText, isCompleted && styles.disabledButtonText]}>
            {buttonText}
          </Text>
        </TouchableOpacity>
      </View>
    )
  }

  /**
   * 渲染头部
   */
  const renderHeader = () => (
        <LinearGradient
      colors={['#FEE9EA', '#FDF1F1']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={styles.header}
        >
          <View style={styles.headerLeft}>
            <Text style={styles.title}>{newbieGiftInfo.title}</Text>
            <Text style={styles.timeLimit}>{newbieGiftInfo.subTitle}</Text>
          </View>
          <View style={{
            height: 49,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            {newbieGiftInfo.icon ? (
              <Image 
                source={{ uri: newbieGiftInfo.icon }} 
                style={styles.headerIcon}
              />
            ) : (
              <Image 
            source={{ uri: GIFT_BOX_ICON }} 
                style={styles.giftBoxIcon}
              />
            )}
          </View>
        </LinearGradient>
  )

  /**
   * 渲染奖励弹窗
   */
  const renderRewardModal = () => (
    <Modal
      visible={modalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleModalClose}
      statusBarTranslucent={true}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <RewardModalContent
          coins={rewardCoins}
          btnText="开心收下"
          title="恭喜获得"
          onPress={handleModalClose}
        />
      </View>
    </Modal>
  )

  // 已移除：新人礼包内的引导动画渲染

  // ==================== 组件渲染 ====================
  
  console.log('新人礼包组件渲染，success:', newbieGiftInfo.success)
  console.log('showListenTaskGuide状态:', showListenTaskGuide)
  
  if (!newbieGiftInfo.success) {
    console.log('新人礼包数据未成功，不渲染组件')
    return null
  }

  return (
    <ScrollAnalyticComp
      itemKey={'NewbieGift'}
      onShow={() => {
        // 曝光埋点已在 useEffect 中实现
      }}
    >
      <View style={styles.container}>
        {renderHeader()}
        
        <View>
          {newbieGiftInfo.gifts.map(renderTaskItem)}
        </View>
        
        {/* 听书任务引导动画：不在新人礼包内展示 */}
      </View>
      
      {renderRewardModal()}
    </ScrollAnalyticComp>
  )
}

export default NewbieGift
