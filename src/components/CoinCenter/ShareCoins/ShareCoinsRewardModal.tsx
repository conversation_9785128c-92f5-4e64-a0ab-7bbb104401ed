import { useEffect } from 'react'
import { useSet<PERSON>tom } from 'jotai'
import { Toast } from '@xmly/rn-sdk'
import {writeShareConinsTaskAtom } from './store'
import {AD_SOURCE, RewardType } from 'constants/ad'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import { RewardModalInfo } from 'atom/listenTaskModal'
import { showRewardModalAtom } from 'atom/listenTaskModal'
import { closeModalAtom } from 'components/common/ModalManager/store'
import type { ModalShow } from 'components/common/ModalManager/types'
import xmlog from 'utilsV2/xmlog'
import { useNavigation } from '@react-navigation/native';

const ShareCoinsRewardModal = ({ onShow }: ModalShow) => {
    const rewardGoldCoin = useRewardGoldCoin()
    const fetchShareCoinsTask = useSetAtom(writeShareConinsTaskAtom)
    const setShowRewardModal = useSetAtom(showRewardModalAtom);
    const closeModal = useSetAtom(closeModalAtom);
    const navigation = useNavigation();

    function onCoinBalanceClick() {
      // 福利中心-金币/现金余额入口  点击事件
      navigation.navigate('CoinDetail');
    }


    useEffect(() => {
    const  receiveAward = async () => {
        try {
          const result = await rewardGoldCoin(
            {
              rewardType: RewardType.RECEIVE_SHARE_COINS,
              sourceName: AD_SOURCE.SHARE_COINS,
              coins: 0,
              adId: 0,
              adResponseId: 0,
              encryptType: '',
              ecpm: '',
              fallbackReq: 0,
            },
            true,
            true
          )
          if (result?.success) {
            fetchShareCoinsTask()
            const modalInfo: RewardModalInfo = {
              showRewardModal: true,
              coins: result?.coins,
              upgradeCoins: result?.upgradeCoins,
              modalType: 'normal',
              title: "瓜分任务开奖",
              subTitle: "在上轮瓜分任务中获得",
              btnText: '兑换现金',
              showAmount: true,
              onCloseModal: (btn = false) => {
                if(btn){
                  onCoinBalanceClick()
                }
                closeModal('ShareCoinsRewardModal', true)
              }
            }
            setShowRewardModal(modalInfo)
            onShow(68294);
            xmlog.event(68294, 'share_coins_reward_modal_show', {
              currPage: 'welfareCenter',
              dialogType: '瓜分奖励弹窗',
              dialogTitle: '瓜分奖励弹窗'
            })
          } else {
            closeModal('ShareCoinsRewardModal', false)
          }
        } catch (e) {
          Toast.info('获取奖励失败')
        }
      }
    receiveAward();
  }, [closeModal, fetchShareCoinsTask, rewardGoldCoin, setShowRewardModal]);

  return null;
}

export default ShareCoinsRewardModal;