import React from 'react'
import { View, Animated, Text, Image, NativeModules } from 'react-native'
import { Touch } from '@xmly/rn-components'
import { px } from 'utils/px'
import { getStyles } from './styles'
import { useAtom, useAtomValue } from 'jotai'
import rewardModalContentThemeAtom, { darkTheme, lightTheme } from './theme'
import { preloadImages } from 'utils/preloadImages'
import { themeAtom } from 'atom/theme'
import ConfirmButton from 'components/CoinCenter/common/ConfirmButton'
import xmlog from 'utilsV2/xmlog'
import { useNavigation } from '@react-navigation/native'
import { showPrizeModalAtom } from 'atom/spinningWheelModal'

// 导出金币图片以便预加载
export const COINS_IMAGE = 'https://imagev2.xmcdn.com/storages/99ff-audiofreehighqps/8C/1E/GKwRIRwL05RWAAA0tAOXmR1U.png'
const THANK_YOU_PIC = 'https://imagev2.xmcdn.com/storages/2219-audiofreehighqps/2D/D9/GKwRIRwMQ6RsAAAgfQPdj_Pv.png'
const IPHONE_PIC = 'https://imagev2.xmcdn.com/storages/b08b-audiofreehighqps/EF/F5/GKwRIJIMQ6RsAAAymgPdj_OQ.png'
const COINS_PIC = 'https://imagev2.xmcdn.com/storages/0a2e-audiofreehighqps/37/23/GAqh9sAMQ6RsAAA1EAPdj_Mx.png'
const RELOAD_PRIZE = 'https://imagev2.xmcdn.com/storages/0661-audiofreehighqps/9C/E3/GAqhfD0MQ6RrAAABpAPdj_LT.png'
export const ROTATE_ICON = "https://imagev2.xmcdn.com/storages/4301-audiofreehighqps/D9/E4/GKwRIUEMTmL3AAADmgPkFyU-.png"
export const ROTATE_ICON_DARK = "https://imagev2.xmcdn.com/storages/9dc6-audiofreehighqps/62/75/GKwRIDoMYNgaAAADbgPt4wly.png"

const ADRESS_URL = ''
const PRIZE_LIST_URL = ''

export const useLotteryImages = () => {
  const theme = useAtomValue(themeAtom)
  const PRELOAD_IMAGES =
    theme === 'dark' ? [darkTheme.popupImage, THANK_YOU_PIC, IPHONE_PIC, COINS_PIC, RELOAD_PRIZE] : [lightTheme.popupImage, THANK_YOU_PIC, IPHONE_PIC, COINS_PIC, RELOAD_PRIZE]

  return () => preloadImages(PRELOAD_IMAGES)
}

// 导出所有需要预加载的图片
export const PRELOAD_IMAGES = [COINS_IMAGE, darkTheme.popupImage, lightTheme.popupImage]

interface RewardModalContentProps {
  coins: number
  btnText?: string
  title?: string
  subTitle?: string
  onPress: () => void
  onReflesh: () => void
  scaleAnim?: Animated.Value
  awardCode?: string
  cashText?: string
  isPhysicalPrize: boolean
  isFirstFree: boolean
  modalType: 'reward' | 'thanks',
  toastIcon?: string
}

export default function PrizeModalContent({
  coins,
  btnText = '确定',
  title = '恭喜获得',
  subTitle,
  scaleAnim,
  onPress,
  onReflesh,
  isPhysicalPrize = false,
  isFirstFree = false,
  cashText,
  awardCode,
  modalType,
  toastIcon,
}: RewardModalContentProps) {
  const theme = useAtomValue(rewardModalContentThemeAtom)
  const styles = getStyles(theme)
  const navigation = useNavigation()
  const [modalInfo, setModalInfo] = useAtom(showPrizeModalAtom)
  // const scaleAnim = useRef(new Animated.Value(0)).current;
  const clickReport = (buttonText: string) => {
    xmlog.click(69124, 'dialogClick', {
      currPage: 'welfareCenter',
      dialogType: isPhysicalPrize ? '超级大奖' : isFirstFree ? '免费' : '付费',
      dialogTitle: title || '',
      Item: buttonText,
      from: isFirstFree ? '免费-大转盘' : '付费-大转盘',
    })
  }
  return (
    <View style={styles.contentContainer}>
      <Animated.View
        style={[
          styles.popupImageContainer,
          {
            transform: [{ scale: scaleAnim || 1 }],
          },
        ]}
      >
        {/* Image组件可以直接使用预加载资源 */}
        <Image
          source={{ uri: modalType === 'thanks' ? theme.popupThanksImage : theme.popupImage }}
          style={styles.popupImage}
        />

        <View style={styles.rewardContainer}>
          <Text style={styles.congratsText}>{title}</Text>
          {!!subTitle && !isPhysicalPrize && modalType === 'reward' && <Text style={styles.subTitleText}>{subTitle}</Text>}
          <View style={[styles.imgContainer, { marginTop: modalType === 'thanks' && isFirstFree ? px(20) : 0 }]}>
            <Image
              // source={{ uri: modalType === 'thanks' ? THANK_YOU_PIC : isPhysicalPrize ? IPHONE_PIC : COINS_PIC }}
              source={{ uri: toastIcon }}
              style={styles.coinIcon}
            />
            {!!cashText && modalType === 'reward' && !isFirstFree && (
              <View style={styles.cashTextIcon}>
                <Text style={{ fontSize: px(12), fontWeight: '600', textAlign: 'center', color: '#FF4444' }}>{cashText}</Text>
              </View>
            )}
          </View>
          {modalType !== 'thanks' && !isPhysicalPrize && <Text style={styles.coinsText}>+{coins}</Text>}
          {modalType !== 'thanks' && isPhysicalPrize && awardCode && <Text style={[styles.codeText, { marginTop: px(20) }]}>兑换码：{awardCode}</Text>}
          {modalType === 'thanks' && (
            <View>
            {subTitle ? (
              <Text style={[styles.thanksText, { marginTop: px(10) }]}>{subTitle}</Text>
            ) : (
              <>
                <Text style={[styles.thanksText, { marginTop: px(10) }]}>大奖不会轻易出现</Text>
                <Text style={styles.thanksText}>再换一次奖池试试手气吧！</Text>
              </>
            )}
          </View>
          )}
        </View>

        <ConfirmButton
          text={btnText}
          onPress={() => {
            // if(modalType === 'reward' && isPhysicalPrize){
            //   NativeModules.Page.start(`${ADRESS_URL}?awardCode=${awardCode}`)
            //   return
            // }
            clickReport(btnText)
            onPress?.()
          }}
          style={styles.confirmButton}
        />

        {!isFirstFree && modalType === 'thanks' && (
          <Touch
            style={styles.subButton}
            onPress={() => {
              clickReport('奖品换一换')
              onReflesh?.()
            }}
          >
            <Image source={{ uri: ROTATE_ICON }} style={styles.rotateIcon} />
            <Text style={styles.prizeRecordText}>奖品换一换</Text>
          </Touch>
        )}
        {modalType !== 'thanks' && isPhysicalPrize && awardCode && (
          <Touch
            style={styles.subButton}
            onPress={() => {
              clickReport('查看奖品')
              setModalInfo({ showRewardModal: false })
              navigation.navigate('RewardRecord')
            }}
          >
            <Text style={styles.prizeRecordText}>查看奖品</Text>
          </Touch>
        )}
      </Animated.View>
    </View>
  )
}
