import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  congratsTextColor: '#FFFFFF',
  popupImage:'https://imagev2.xmcdn.com/storages/aa59-audiofreehighqps/00/CD/GKwRIJELzkB7AABqLwOUat8G.png',
  popupThanksImage: "https://imagev2.xmcdn.com/storages/7b9b-audiofreehighqps/60/28/GAqhp50MTm9bAAAIbgPkHzys.png",
  subTitleTextColor: '#8D8D91',
  thanksTextColor: 'rgba(255, 255, 255, 0.5)',
  codeTextColor: '#FFFFFF',
};

export const lightTheme = {
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  congratsTextColor: '#491414',
  popupImage:'https://imagev2.xmcdn.com/storages/7fe8-audiofreehighqps/C9/52/GAqhfD0MYOMPAABk7QPt68Wn.png',
  popupThanksImage:'https://imagev2.xmcdn.com/storages/7c4e-audiofreehighqps/80/C0/GAqhfD0MTm9HAAAImgPkHzEO.png',
  subTitleTextColor: 'rgba(44, 44, 60, 0.4)',
  thanksTextColor: 'rgba(80, 24, 24, 0.5)',
  codeTextColor: '#491414',
};

const lotteryModalThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default lotteryModalThemeAtom; 