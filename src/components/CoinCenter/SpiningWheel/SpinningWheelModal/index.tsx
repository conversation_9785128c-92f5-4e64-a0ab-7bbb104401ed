import React, { useEffect, useState, useCallback, useRef } from 'react';
import { View, Animated, TouchableWithoutFeedback, Easing, Modal as RNModal, StyleSheet } from 'react-native';
import { useAtomValue, useAtom } from 'jotai';
import { Toast } from "@xmly/rn-sdk";
import { themeAtom } from 'atom/theme';
import { showPrizeModalAtom } from 'atom/spinningWheelModal'
import { useVideoResources } from 'hooks/useVideoResources';
import { refleshFlagAtom } from '../store'
import { LISTEN_TASK_POSITION, AD_SOURCE, RewardType, FallbackReqType } from "constants/ad";
import watchAd from "utils/watchAd";
import { getStyles } from './styles';
import PrizeModalContent from 'components/CoinCenter/PrizeModalContent';
import ConfettiAnimation from 'components/CoinCenter/common/ConfettiAnimation';
import CloseIcon from 'componentsV2/common/CloseIcon';
import AdRewardModalContent from 'components/CoinCenter/AdRewardModalContent';
import xmlog from 'utilsV2/xmlog';
import { videoRewardSources } from './source';
import AlphaVideo from 'components/common/AlphaVideo';
const customEasing = Easing.bezier(0.66, 0, 0.34, 1);

// 视频资源链接
export const VIDEOS = {
  BACKGROUND_START: 'https://aod.cos.tx.xmcdn.com/storages/e783-audiofreehighqps/30/A0/GKwRIaILzX4rAANaOAOUBfnN.mp4',
  BACKGROUND_LOOP: 'https://aod.cos.tx.xmcdn.com/storages/2f20-audiofreehighqps/03/59/GKwRIJILzX4rAAQDGQOUBflt.mp4',
};

// 填写地址页面链接 TODO: 填写链接地址
const addressUrl = '';
// 查看奖品列表链接
const prizeListUrl = '';

const THANK_YOU_PIC = 'https://imagev2.xmcdn.com/storages/2219-audiofreehighqps/2D/D9/GKwRIRwMQ6RsAAAgfQPdj_Pv.png';
const IPHONE_PIC = 'https://imagev2.xmcdn.com/storages/b08b-audiofreehighqps/EF/F5/GKwRIJIMQ6RsAAAymgPdj_OQ.png';
const COINS_PIC = 'https://imagev2.xmcdn.com/storages/0a2e-audiofreehighqps/37/23/GAqh9sAMQ6RsAAA1EAPdj_Mx.png'
const RELOAD_PRIZE = 'https://imagev2.xmcdn.com/storages/0661-audiofreehighqps/9C/E3/GAqhfD0MQ6RrAAABpAPdj_LT.png'


// 背景开始视频预计时长（毫秒）
const BACKGROUND_START_DURATION = 2000; // 根据实际视频长度调整
// 预加载背景循环的提前时间（毫秒）
const PRELOAD_LOOP_TIME = 100;

enum VideoPlayState {
  BACKGROUND_START_AND_CONFETTI,
  BACKGROUND_LOOP,
  FINISHED
}

// 导出预加载资源的hook
export const useModalVideos = () => {
  return useVideoResources(videoRewardSources, { autoLoad: false });
}

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  onPress?: () => void;
  onReflesh?: () => void;
  children?: React.ReactNode;
  coins?: number;
  type?: 'withAd' | 'normal';
  upgradeCoins?: number;
}

export default function Modal({
  visible,
  onClose,
  onPress,
  onReflesh,
}: ModalProps) {
  const theme = useAtomValue(themeAtom);
  const styles = getStyles(theme === 'dark' ? 'dark' : 'light');
  const [playState, setPlayState] = useState<VideoPlayState>(VideoPlayState.BACKGROUND_START_AND_CONFETTI);
  const [showContent, setShowContent] = useState(false);
  const [refleshFlag, setRefleshFlag] = useAtom(refleshFlagAtom)
  const [backgroundLoopVisible, setBackgroundLoopVisible] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [modalInfo, setModalInfo] = useAtom(showPrizeModalAtom)

  // 添加一个定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  // 背景视频开始加载的时间
  const bgStartLoadTimeRef = useRef<number>(0);

  const getBtnText = (): string => {
    const { modalType, isFirstFree, isPhysicalPrize } = modalInfo
    if (isPhysicalPrize) {
      return '填写地址'
    }
    if (isFirstFree) {
      if (modalType === 'reward') {
        return '开心收下'
      } else {
        return '我知道了'
      }
    } else {
      if (modalType === 'reward') {
        return '再抽一次'
      } else {
        return '立即抽奖'
      }
    }
  }

  // 创建动画值
  const scaleAnim = useRef(new Animated.Value(0)).current;

  // 使用 useVideoResources hook 加载视频资源
  // const { paths } = useVideoResources(VIDEOS, {
  //   autoLoad: modalInfo.showRewardModal, // 当弹窗显示时自动加载资源
  // });
  const { paths } = useVideoResources(videoRewardSources, {
    autoLoad: modalInfo.showRewardModal, // 当弹窗显示时自动加载资源
  });

  const renderBackgroundVideos = () => {
    return (
      <>
        {paths.awardBg ? <AlphaVideo
          source={paths.awardBg}
          style={{
            ...StyleSheet.absoluteFillObject,
            width: '100%',
            height: '100%',
          }}
          repeat={true} /> : null}
      </>
    )
  }

  // 背景开始视频加载完成回调
  const handleBgStartLoad = useCallback(() => {
    // 记录背景视频开始加载的时间
    bgStartLoadTimeRef.current = Date.now();

    // 计算预加载时间
    const preloadDelay = Math.max(BACKGROUND_START_DURATION - PRELOAD_LOOP_TIME, 0);

    // 清除可能存在的旧定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 设置新的定时器，提前预加载BACKGROUND_LOOP
    timerRef.current = setTimeout(() => {
      setBackgroundLoopVisible(true);
    }, preloadDelay);
  }, []);

  // 背景视频播放完成回调
  const handleBgVideoComplete = useCallback(() => {
    // 如果还没有显示背景循环视频，立即显示
    if (!backgroundLoopVisible) {
      setBackgroundLoopVisible(true);
    }

    // 清除定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, [backgroundLoopVisible]);

  // 初始动画已经不需要，直接在visible变化时触发动画
  useEffect(() => {
    if (modalInfo.showRewardModal) {
      // todo 弹窗曝光埋点
      xmlog.event(69123, 'dialogView', {
        currPage: 'welfareCenter',
        dialogType: modalInfo.isPhysicalPrize ? '超级大奖' : modalInfo.isFirstFree ? '免费' : '付费',
        dialogTitle: modalInfo.title || '',
      })
      // 显示内容并启动缩放动画
      setShowContent(true);
      setShowConfetti(true);

      // 启动缩放动画
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        easing: customEasing,
        useNativeDriver: true
      }).start();
    }
  }, [modalInfo.showRewardModal, scaleAnim]);

  // 当弹窗关闭时重置状态和清除定时器
  useEffect(() => {
    if (!modalInfo.showRewardModal) {
      setPlayState(VideoPlayState.BACKGROUND_START_AND_CONFETTI);
      setShowContent(false);
      setShowConfetti(false);
      scaleAnim.setValue(0);
      setBackgroundLoopVisible(false);
      bgStartLoadTimeRef.current = 0;

      // 清除定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    }
  }, [modalInfo.showRewardModal, scaleAnim]);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // 渲染视频内容
  const renderVideo = () => {
    switch (playState) {
      case VideoPlayState.BACKGROUND_START_AND_CONFETTI:
        return (
          <>
            {showConfetti && (
              <ConfettiAnimation
                autoPlay
                loop={false}
              />
            )}
          </>
        );

      default:
        return null;
    }
  };

  const clickReport = (buttonText: string) => {
    xmlog.click(69124, 'dialogClick', {
      currPage: 'welfareCenter',
      dialogType: modalInfo.isPhysicalPrize ? '超级大奖' : modalInfo.isFirstFree ? '免费' : '付费',
      dialogTitle: modalInfo.title || '',
      Item: buttonText,
      from: modalInfo.isFirstFree ? '免费-大转盘' : '付费-大转盘',
    })
  }

  if (!modalInfo.showRewardModal || !modalInfo.modalType) return null;

  return (
    <RNModal
      visible={modalInfo.showRewardModal}
      transparent={true}
      animationType='fade'
      onRequestClose={() => {
        clickReport('关闭')
        setModalInfo({ showRewardModal: false })
        onClose?.()
      }}
      statusBarTranslucent={true}
    >
      <View style={styles.container}>
        <View style={styles.modalContainer}>
          {/* 关闭按钮 */}
          <TouchableWithoutFeedback onPress={() => {
            clickReport('关闭')
            setModalInfo({
              showRewardModal: false,
            })
            onClose?.()
          }}>
            <View style={styles.closeButton}>
              <CloseIcon size={16} color="#FFFFFF" />
            </View>
          </TouchableWithoutFeedback>

          {/* 渲染视频内容 */}
          {modalInfo.modalType === 'reward' ? renderVideo() : null}
          {/* {modalInfo.modalType === 'reward' ? renderBackgroundVideos() : null} */}

          {/* 显示内容 */}
          {showContent ? (
            <PrizeModalContent
              coins={modalInfo.coins || 0}
              title={modalInfo.title}
              subTitle={modalInfo.subTitle}
              cashText={modalInfo.cashText}
              awardCode={modalInfo.awardCode}
              isFirstFree={!!modalInfo.isFirstFree}
              isPhysicalPrize={!!modalInfo.isPhysicalPrize}
              onPress={() => {
                setModalInfo({ showRewardModal: false })
                onPress?.()
              }}
              onReflesh={() => {
                setModalInfo({ showRewardModal: false })
                onReflesh?.()
              }}
              modalType={modalInfo.modalType}
              scaleAnim={scaleAnim}
              btnText={getBtnText()}
              toastIcon={modalInfo.toastIcon}
            />
          ) : null}
        </View>
      </View>
    </RNModal>
  );
} 