import FlipCard from 'componentsV2/FlipCard'
import React from 'react'
import { EasingFunction } from 'react-native'

type Props = {
    backSlot: React.ReactElement
    delay?: number
    duration: number
    easing?: EasingFunction
    fire: boolean
    frontSlot: React.ReactElement
}


export default function ({ frontSlot, backSlot, duration = 600, fire = false }: Props) {
    return (
        <FlipCard
            frontSlot={frontSlot}
            backSlot={backSlot}
            duration={duration}
            fire={fire}
        />
    )
}