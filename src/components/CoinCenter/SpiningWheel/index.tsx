// SpiningWheel组件主入口

import React, { useCallback, useEffect, useRef, useState } from 'react'
import { View, Text, TouchableOpacity, Image, Clipboard } from 'react-native'
import { useAtomValue, useSet<PERSON>tom, useAtom } from 'jotai'
import { Toast } from '@xmly/rn-sdk'
import { px } from 'utils/px'
import { spinningWheelAtom, writeSpinningWheelAtom, refleshFlagAtom, prevPrizesAtom, remainingSecondsToMidnightAtom, needsRefreshAtom, updateRefreshTime<PERSON>tom, lastRefreshTimeAtom } from './store'
import { themeAtom } from 'atom/theme'
import { showPrizeModalAtom } from 'atom/spinningWheelModal'
import { getStyles } from './styles'
import { SpiningWheelProps } from './types'
import { finalSpinningWheelThemeAtom, spinningWheelThemeGridAtom } from './theme'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { AD_SOURCE, RewardType, FallbackReqType, LISTEN_TASK_POSITION } from 'constants/ad'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import { balanceAtom } from "atom/welfare";
import watchAd from 'utils/watchAd'
import useThrottleCallback from 'hooks/useThrottleCallback'
import ModuleCard from '../common/ModuleCard'
import WinnerMarquee from './WinnerMarquee'
import { useNavigation } from '@react-navigation/native'
import { PrizeModalInfo, initialState } from 'atom/spinningWheelModal'
import SpinningWheelModal from './SpinningWheelModal'
import Lattice from './Lattice'
import { ROTATE_ICON ,ROTATE_ICON_DARK} from '../PrizeModalContent'
import { updateWelfareAtom } from 'atom/welfare'
import xmlog from 'utilsV2/xmlog'
import AnimatedLottieView from 'lottie-react-native'
import GlobalEventEmitter from 'utilsV2/globalEventEmitter'

const PRIZE_NAV_PIC = "https://imagev2.xmcdn.com/storages/db91-audiofreehighqps/4C/95/GAqhF9kMTTilAAABjQPjfZKg.png"
const CONSUME_TYPE = 5;

const CURRENT_PAGE = { currentPage: 'welfareCenter' }
interface ReportProps {
  id: number
  module: string
  currPage: any
}

export const SpiningWheel: React.FC<SpiningWheelProps> = ({ onError, onSpinStart, disabled = false, autoRefresh = true }) => {
  const spinningWheelInfo = useAtomValue(spinningWheelAtom)
  const fetchSpinningWheel = useSetAtom(writeSpinningWheelAtom)
  const [refleshFlag, setRefleshFlag] = useAtom(refleshFlagAtom)
  const balance = useAtomValue(balanceAtom);
  const setModalInfo = useSetAtom(showPrizeModalAtom)
  const modalInfo = useAtomValue(showPrizeModalAtom)
  const rewardGoldCoin = useRewardGoldCoin()
  const theme = useAtomValue(spinningWheelThemeGridAtom)
  const navigation = useNavigation()
  const spinResultRef = useRef<PrizeModalInfo>(initialState);
  const finalSpinningWheelTheme = useAtomValue(finalSpinningWheelThemeAtom)
  const globalTheme = useAtomValue(themeAtom)
  const [flipping, setFlipping] = React.useState(false)
  const prevPrizes = useAtomValue(prevPrizesAtom)
  const remainingSecondsToMidnight = useAtomValue(remainingSecondsToMidnightAtom)
  const updateRefreshTime = useSetAtom(updateRefreshTimeAtom)
  const lastRefreshTime = useAtomValue(lastRefreshTimeAtom)
  const updateWelfare = useSetAtom(updateWelfareAtom)

  const [isSpinning, setIsSpinning] = React.useState(false)
  const [currentHighlightIndex, setCurrentHighlightIndex] = React.useState(-1)
  const [isRefreshing, setIsRefreshing] = React.useState(false)
  const [showGuideHand, setShowGuideHand] = React.useState(true)
  
  // 定位相关
  const spinningWheelRef = useRef<View>(null)
  const [isLayoutReady, setIsLayoutReady] = React.useState(false)

  

  // 移除硬编码的样式配置，使用主题系统
  const styles = getStyles(finalSpinningWheelTheme)

  // 添加组件挂载状态检查
  const isMountedRef = useRef(true)
  useEffect(() => {
    return () => { isMountedRef.current = false }; // 卸载时标记为 false
  }, []);

  // 创建安全的 fetchSpinningWheel 包装函数
  const safeFetchSpinningWheel = useCallback(async () => {
    if (!isMountedRef.current) {
      console.log('组件已卸载，跳过 fetchSpinningWheel')
      return
    }
    return fetchSpinningWheel(isMountedRef.current)
  }, [fetchSpinningWheel])

  useEffect(() => {
    if (autoRefresh && isMountedRef.current) {
      safeFetchSpinningWheel()
    }
  }, [autoRefresh, safeFetchSpinningWheel])

  // 监听定位事件
  useEffect(() => {
    const locateListener = GlobalEventEmitter.addListener('locateSpinningWheel', () => {
      // 当收到定位事件时，触发大转盘任务模块的定位
      if (isLayoutReady && spinningWheelRef.current) {
        spinningWheelRef.current.measure((x, y, width, height, pageX, pageY) => {
          // 触发页面滚动到大转盘任务模块位置
          GlobalEventEmitter.emit('scrollToSpinningWheel', { y: pageY });
        });
      } else {
        // 如果布局还没准备好，延迟重试
        setTimeout(() => {
          if (spinningWheelRef.current) {
            spinningWheelRef.current.measure((x, y, width, height, pageX, pageY) => {
              GlobalEventEmitter.emit('scrollToSpinningWheel', { y: pageY });
            });
          }
        }, 500);
      }
    });

    return () => {
      locateListener?.remove();
    };
  }, [isLayoutReady]);

  // 控制指引手显示逻辑
  useEffect(() => {
    // 只有在首次免费且未开始抽奖时显示指引手
    if (spinningWheelInfo.firstFree && !isSpinning && !isRefreshing) {
      setShowGuideHand(true)
    } else {
      setShowGuideHand(false)
    }
  }, [spinningWheelInfo.firstFree, isSpinning, isRefreshing])

  // 渲染指引手
  const renderGuideHand = () => {
    if (!showGuideHand || !spinningWheelInfo.firstFree) {
      return null
    }

    
      //console.log('renderGuideHand     啊啊啊啊啊啊啊啊啊啊啊')
      
      // 直接使用JSON对象，避免网络请求
      const lottieSource = {"v":"5.6.3","fr":25,"ip":9,"op":113,"w":162,"h":162,"nm":"容器 10080@3x","ddd":0,"assets":[{"id":"image_0","w":96,"h":81,"u":"","p":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABRCAYAAAAtvqMwAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAV4ElEQVR4nO1da6wdV3X+1t4zc+bcdxKbkITECYkbnknBgUCSgg2piig0qATDDx6qrKaCqqpaoVQCQVWQ+gNIAaGqVdVKlaC0xLQlVRUCCHwJ5EWciGAFSkJy27xIfG1f+17fe86Z2Xut/tiP2XNsaONz7k1svKTxuWdmzsye9e31XnsMnKZTh0SE9u6VXES0iNBzPZ6TgcbCJBGh/pH+hTRdXgfgUq3QF4t9uca3ATxORDyO+5ymX0BHl5Z+vR70flCZuqotc2WZa5ZBxfJQZeUPnnpKJp7rMZ6yJCKqt7j/n6uVJa6OLkvVW5Oq35Nq0JfKGKksL9XW/oXIaRCORyOroN6jT21RpfwEhe6CNEAapDNAEaAUkOdQWh+pe70/LCcn/4WI7DgGfqpQNvIVpPcmGNUBckALAAux1jFfKRAzuMhn8zL/RG95+REAd498z1OI1MhXsPVFYiuCqQFTAVwDpgbqGqgMpF8BVQWBXKiz7KNHHn/8zDGM+5ShkQAQEVJ1JTBGxNSAtYBlgC3ABrAGsBZSGcjAKNXJf6ucnb1B9u7Nx/UAJzuNLAF11V8gywZsIdZALAMsgDh1BDaAsZDaQNjm1Mk+2N+y5erTcYKjkQAgIsm1uhvMK47pAJgBYQeACEAEiHXSUNWAzs/Ppqc+ubq4ePaYnuGkppEloCimnmRj7ycRcarHQjhIARwgEMAKYASoDSHLXltMlB+WxcXp0R/h5CY96gVe/oY32IvnJud0ll0LIg1ymBKRm/3pBv+hSSPrvJKVelwXxQPz8/My6jhOVhqLHl66/44t3bxzG4rOS6By54JmGYh0dEfdlrn9ZQ4UOcjIE3VVvbs7PX0XEf1KgjC6GwrgkXs7T9mad4PFQHzahxlAYgtYnG1gBmoLGAPJ9YuyIv/L/tNPbxm+pkBoYWGhXF1cPHfxscVzl5eXzzoVk3xje5j9d+65ZHp69lbKi63QClAa0BlIZW72kwK0Asjv72RAngFZbqmqv7Z2sL5h9vzZQwBw8OGHZ8qpud8oZjq7kHcuFtC0APvJ8A9Mj7/cHXR/ROfSWrj3zTffrN9yzTVnlPnEhUJygc1IkcjiYDB4rFJqcfPmzWvP14Tg2ADYs2dP9tq5iT/Lis6fS5bn0F79BBBI+fREBmgNZBpUZECuQSrv89rgEwePLn9+oq473SL7jJqa+l0pO7NQBTkbAgBgMXyE6sE/rq2s/PXn/uZvFnbt2tXZXJbvU3m2C7neiryYIqUtoAbC9gCs7EVdf8Nk6pvdbvcpIpJEihQANT8/L9u3b2cAstGqcKzivHTHt7d0p2ZuQ1G+BDpDkATS2kkEkWN+kIJcOynICxDRYbt69ONy4MCVamrq3dTtZigLkC7c+eScKgAAi6W6usMsL38UK6tvVpPljegUE6Sd7UGmQTp3YAsEIj0xZp85uvZZW/ODhcblKOliIX2ByrMOM61oKh61pv7RwA72zszMHN4oiRkrACJCaz+850M6L29CXnSiKlLKgUDNd6eiNCjLgDwH8gzEvGqfeaZLCgoTE6AiB8oOSOUAdBytwOPRWz3I+xenkOkSeQFkXrKyDOgUINXx5wsIIqgHh2V5xULTNLIiRycnyjK4JJYChA9BZF/d633hyWeeuXXr1q2DcfLneDR2g3Z0391n56y+hk73SlGaIqMjAMEeBBWlQbkHIcuBQR988ACoLECdEig6QOE9KqcxmpGLQFaWwQcOgHIF5B2v2nKgLEFZAYAgYjxqBPT6kCPL7t7dDqgsndQALoUiJERq2fQHN5X7Zz5FW2ldQRiLF5TS5Cuu3F9VfBPYHnUP5CJjCREyCwDx+SIGrEBqC9QGsDWoLKHO3AQZ9CB1BRgGDEPYArAA2H16b4umZoCpKdjDy5C1nkt7WAGYIWwgbPx92OWqigyYKMH9AbDWA/p9F6HXxueyLAnbWZ2pG9cmf/4BERk9Y/xLaOwAEJHYzsS3pTbfhbCAOTJbQo4ouqb+mBWIsS5xJxbU7QKTZ4CXVyFVz7mt1oMIgwgCLEAMfdYm4IwzwMvLwGCAaC0E/j7hb/fIVE6AOiWkqh34JrjLXiEwA6Sm8snJG9ceffxVsg6aItDYAQCAucsuO2yq+vNieSnGADFHlMQGYJcnEj9LjQVqhoChZ2dBk1PgpSXIoOdmqDEQ61IdIi6uELEQYmTnnAu1ZQuk0wF1CiAvvMH3g/K3gwigCWpuGtQt3XgUfIiebgDy/CIq848fXliYXQ8+AesEAAA5WkzcBeb/cNxKVJE0EuFA8GBY66Sg9uoAArVpM2RiDvbQIUi/52aqbTaXc/KgQqBm56BecA6oOw1SBYhUKw0SicjZipkZ0MQkUHaBwhtxlaROFFQ2WW4vlfodEVkXXq0XAHjh5ZevDlarL4ip9seZz27GS0jQsVcPAQy2EFMDVQ0xNQiC7AWbwFkHdnUtOS9IEfzM9jbF1k6axAJSQ8Q2IJMAZBHVk1JOSsoSIAVSOSgvHBC5brJkRTapcv3h/k8Wzl8PPq0bAAAwm+f7yJp/ArONBpkTMBA2cUzzqsqBMIBUfQAWxYvOA03POi8JQGPETSJJYb8zvGKDlAEANSom2gIBKQJpLyUQAApEmYs9dAZoAqAIRfdlKPA+uVlGTl4O07oCQFdcUVcm+3up68daOSEOqogT++BzRUG9VNZ5J4MaACObnQY6JZAVzQ2iBCBRa2giNklOFACinKFlNw7xk8JpKEH6YwpqiwAopVWneO/Kr/3XxePm0boCAADT27b9VIz8LZjrhtFuC4a08Yi8UWbnkkpdQwYV0K8gla83wzo9HVVPYtiRuLrhWFBzftY3Nkcc0MZAbOXcVbEQEWfYrZdIK8FmbM2ni9/bs2fPWN3SdQeAiGTQN18hK/tCwSaqIE7igxSEwDjLgKkh1QAY9J2LaaxjKMjN5ggEmjAhBTZlPMJ+TkCAKxQZb3/MwHtctjH6bAEoRUpf9/rzzrtgnPxZdwAAYO6qqx5jxufA3G8Yk6if1EUNM1Rso1YMQ2oLqSonEXXlSpwxrhie9TaZ+Wkq/DgxCLv4A4Yd42sTPTGpaog1TiLFQGVqKwntHKdHtCEAEJEcWe19A8zfA4fgrKkPSCoJLQamwVpgki/w1zXEVq4FxjYdGE1nRvK7EAnzEOjWxx7G+Gtav7nv4RiMC9JYKGOl37by0ENja63ZsOKGiFD/gfu2Q6uvktZnxqwoka8TEChUzmgoICLlgyXV+PTRxw+nEVp5IjT7icjbm+SYwKuwEBQCxxaQwubPZwZMfaRa7r9j+lUvnR8HXzZEAgAnBeVZZ9/NpvpXiLVNANWojygJsa0l0dvB1+dkRlv2xf4k12PZSYOPqkNLDIyXEGNjRQ5Rgvz3kJOqTSNp1kLYOtcWDFE0g5zeNa7epg0DAADo/PN7q4d7n2IjD6Y5olaglqqktLuipevh//YqRjzTQ2xgAhheNZlUnaSbP8fUcZ/ErWrAqj1wzjOiXNPrD6ytbRoHTzYUAADYvH37I2Lwd2BbtQxy6rVY56EIC0SGwEhry5L8zeF3CbDWNh7N8OwPzWLGu7ue8fG8YDNsZDxiJlerrZ3Z2UvGwY8NB+DQPfecp8tiB0hnLV899UxSf53F++eJZNhGWlr6elg9sfPnxRqIsd7nr90+k6iplvpKpIxTyQopcAGUmiBkbxlHTLCuue5hevqBBya7mv4UItdBSAlbnwUggJUzkCwAsQu2AN9Zl9QjyULiPgKBhlwJ8WlvIHZoBGML+M+0vgk44wu/XyAefMLw7wBAA1pRVuirXpGf0wWwMgpPNgwAEaHVfT98h0K2C5BMWIDKOE8I0pQro6cSM5LhCr4KFpgnaPjjQQj7o1OT2I8gUfDXoOS8uD85F8lhODCI/ZiYCEpd2plaOwcnCwCL99579lRZfAiQGQDO2PYHkIFAzU54dzIkzQLDCLB+oQcAx3H256jGPQzMjABI4mIGKUDCXJ8P8tcUERDEC4d3WcOlIE1XX0iBEEBan0kqvxzAw2iweta0ITZARKgs9OtJ6W0gzzgWF20eXoYMaiCkp1NvJ3pH0mzht8FeiI1Gu5XKSA17K73R2B1XqmiqdcwCa135U4TB3ikIlTyJ3pgASuUEvW3URrGNkgCV5fn1BHKpTELjvRiGrKyBOrlTQ8JoV6YIUUcfU1wJM9Uz2hfq3b5E5XCyD41QOH2PZqaHoYVr+XOIAIFb7RMTgaRJaVzy8/vuKwHEJrFnSxsCwGDfvi1K620Ij+WZGB5ZVgeQyRrU1YAalmb2Ojvk8v3xAIR4lSXeFY1NK20dH2evtxHuq1cv4Xgrl+3HKAKCAiEx6NAAhATy4rzfH6lGsBEAkGHemmV6S9wj5KpQpNzzWAtZ6bkeIBkGIDCJAFFtjyemF7xtOI5RbamNeEzcZYQRNEgEBP4W/tpEBIJ191aEWEMiBZXrc7OJiTMxgiFedxvgZ9llgBSNCvEdcnnhPB8IJKSa0czSOGMZ7YxmtAfhuJ/9wT60Oi+GvgPNtf2nsICYHfjibAOJuI2H7FGIwAUAQxeSjRQRr78R3r1b6YwuQUzhOhAo06BOB1QUUa9Kwv84fTlhXCx/BYZ45odpGT2eJK80HG37gCrMdleA4QgGsWM8/GcAhTiormY8BOpU1kyOwp71B+Bd7wIz51AkjXp1riVNFMDUFKiYAk1Pu442JGXDSENeTavI3/aeGsYnEpIAE75T2iwmgCTgRACFIWJArToFot2AC8kmMUJWef1twO7dUC+71C+bAVoPorRrC+l0gdzHZP6Uxsj6fbFpOVUjXnKCgQbaxjd+H97nZnLj77fth7tzMP7uXCJxETqFuEQA4lzpwSTaV39WtO4ScN+LX6wg0jumLQQAQKBMgTqZ7x2lJq1PSXIuaJ6g5309WYS9SggzFP8H8xsPyAXS1Pp0tiW0tbiLSaum7JyHWA5VSoP15lFigXUHYNu2bQbCT5KAYxqBBFDsmKzh1w34hwtFmhYzufmSmIHGmKYIJeoi/jbYCJfnoZbagtP3cGqJkqQgWev2xZspnyXx1yYtOuuM9A6MDSnK17baJyx1ZJQk0xBBtNGkI1ozzc92DpvrXDgGgJaHw4jdeNGrcvemWHv2t/b2IBpdNPskXtuPOeSclM9RKQKr0Zp3NyQVoQa4W0z1s0TRJoxLDGw4FvVE8DrCb7hhvE9Tt9xNHgI3BGeSbuIZHxguIGEoz3Rijh5PuLakaizNVUFIIfPInCBvTvSHz4bu7fUOgu0tbkV9YHbiRraK8Yzjzu5QDEln5LBHFF3WcH0/gGPiiXAt3/ae3ieZIxR3Jwi269XCzD6RdWK0IQDs2LHDKJEvi6n/2zETiY5vUgySJsrCg3PClLRwE2Z3WiWLNeYw8xOpGF7BHyWAGiOcAtZSQUPjiKcRZRmPtIBjwypixc+u/KkVvkms7QWdHixqZLa4rKSkLSQtpqSM9OsD0tkZmB9tRBq9JnbCXRBImR93J7EFnHFu7u3tD8GlUtgyi/x8lIV9G9cVsZPs0qGV3WLq/wQb03RBSJtBabNU2gWRtKEDSGZsojqiJEgrqIqvS4iti4l6ipE0gKEpTiz+lgIO5Ulj4325ro+sLC09MgpfNrQm/KJrrz00qOVGsL0dYgQ2mdWhAB7z/OFTfOeDNEyPYCXGl5Pjx+SAkIDoB5OomUYwvOcV/3VGOkpTHKM4CayrH3eIHh+FJxtdlJe5173uf2yFPyG290OYm64DO8TUIRBaSbhUGoaNsSSzWoa2tnsqLZU0TNTUBkJ6QhLptGCQvnP2mmuOjMKQDe+KICKZvPLKfYP+4INi7J1gMY1HJI0hHTKYLX0+bBzT2d4K2oaYHwcBROCA5nxqWeJ4jRh1B2dBAFDOlunR5AcnRBsOAOBAmLnqjXsrlX2AgFtIUDceTKpWgmva7qg+pvSIXwBQNOIJjyiAkUpIGj8Ect+9mXbtjX61P2kN0kS6zLsYIQYAniMAAAfC3Gtes3Do6cX3C8unSXgVbCWCkOp6SAOGmLZxTfuDWiD4XvUYxXqKE9sxvu3ABEnwkbhyfaUhTaK0710FQZQoUnTZj3fvfv5Hwr+E5Ny3v33tiYNHPlFZvAegewDpO/cyjXLDyki00xIhSg1JtigQiQqKaqj5iAm4eCoNAUVJYlA1K/1VMNLskiU6e/NFL33pSG/+eq4BAABsfetbB9PfvObWo8u9dwrkYwQ8CJLKxQic8FDa0aw/4M5pdLzbHaNV/5nq9+G/JGmLUU3KmbSXArfKP5gOWHaxSqZfYFmP9P67kfTXepCI6MGdd15oYK7PIDuZ+TKANISbpOTwqAmIhXtCPCGu83IZpWTWU/O7uF+5c/wbv4KLSkSuFB1er6AzIMtB3S5oYpLl6NFPdy695CMn+nKP54UEpEREtrz66kcmn3zmM6zL6wh6Fymah9ZLLYU97GHG5Fvj3Uj0YPxh/53Ir5BUCqR08+IQFV6p49/yosLM9xUwa93rE6q+W61TDZSw3TQ/P3/CfNzQ3tBnQ7RzpwXwhIh8EV//+ld6U1NXALgOiq+H4HwiaBEmDM1mAIiBk08ZxGvG99epRtXAV7eG2xujJHn74pN7IgQwg3QFELFUfGj7d797QrN/eNjPexIRdeiee6byqnqTsvU7ibCDCC+E+GXV8Wk84wKzAddWGBiv/FtXKP1R4obGXiPvojJi/VhYnFSUJYjUGlN+/cQVr7ztRPNBz1sJOB55PbssIrdgfv62FZ1doGTwDkX0HoJcKqK6aLdSu3/j0qd0iROGUsuNNxWL7hGHJDYRAWUZyDJDmX+bmC6/N0oy7qSSgOORiCjcddfcat1/mxZ6LxRfLaAuBASlvPFUCcO9nm+1PsaLJS4sIsOFQ/+pBbQCZZmF8Pcr5t+ffeMbHx5l/CeVBByPvFQcEpEvHr3zm7dlUvwmGH8MhVcD0G3PKOj5ZDFfmLycSIJPOUDgmnetX5vsatcDZvsta8yHP3v77SNlQsMQTimSm2/Wi5tnLprMys8qot+GUnTsC2TT1ZRpSwsQy55s4zuMAAE0CUg9wZb/ioEvTe/YcXCoknBCdMoBEGjpO9+5vCz0V6HUxQCSNy8mdmDYdWJCWNYE6yNjrQSEg8L2380A/3B/Qfft2LHDjGucJ70K+kU0d/vt+5Z3vOGTHeALEhaFhBkfF3wIIOSdnWbmOwNMFlovCuTrLPSlx/Yf+P7Ld+6sxj3OU1YCAGDxllumJ8+a/RiJ/JEIlwAaD6dlfOG8Gy0MVj0h/VPLcpupZPfcwkMP4oYbzHq9T/SUBgAADn3rW7NFJ/+IEvt+sGxyCR4OZS8mIsugVYJaEPAdJPiOEfW96e3blzbi/7s55QEAANmzJ+t1Oq8WY64VU79CgScBrAr0giVZEDH7iKpHpjF1GNu321/VF4mvO4mI2rNnT/bwrbd2/IvA1an2MvDTdJpO02k6TafpNP0/6X8Bx97i/opVErMAAAAASUVORK5CYII=","e":1}],"layers":[{"ddd":0,"ind":1,"ty":2,"nm":"容器 <EMAIL>","cl":"png","refId":"image_0","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[0]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":9,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":113,"s":[100]},{"t":126,"s":[0]}],"ix":11},"r":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":9,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":21,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":33,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":46,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":59,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":72,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":86,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.167],"y":[0]},"t":99,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.167],"y":[0]},"t":113,"s":[0]},{"t":126,"s":[0]}],"ix":10},"p":{"a":1,"k":[{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":9,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":21,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":33,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":46,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":59,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":72,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.333,"y":0},"t":86,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":1},"o":{"x":0.167,"y":0},"t":99,"s":[63,70,0],"to":[0,0,0],"ti":[0,0,0]},{"i":{"x":0.667,"y":0.667},"o":{"x":0.167,"y":0.167},"t":113,"s":[107,112,0],"to":[0,0,0],"ti":[0,0,0]},{"t":126,"s":[107,112,0]}],"ix":2},"a":{"a":0,"k":[48,40.5,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"ip":0,"op":250,"st":0,"bm":0}],"markers":[]}

      
      return (
        <View
          style={{
            position: 'absolute',
            right: px(105),
            bottom: px(105),
            width: px(54),
            height: px(54),
            zIndex: 999,
          }}
          pointerEvents="none"
        >
          <AnimatedLottieView
            source={lottieSource}
            style={{
              width: '100%',
              height: '100%',
            }}
            autoPlay={true}
            loop={true}
            speed={1.0}
            resizeMode="cover"
          />
        </View>
      )
    
  }

  // todo 免费机会刷新，请重新加载
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null
    
    if (!spinningWheelInfo.firstFree) {
      timer = setTimeout(async () => {
        // 检查组件是否已卸载
        if (!isMountedRef.current) {
          return
        }
        
        if (isSpinning) {
          // 等动画结束在更新
          return
        }
        
        try {
          await safeFetchSpinningWheel()
          
          // 再次检查组件是否已卸载
          if (isMountedRef.current) {
            Toast.info("免费机会已刷新～")
          }
        } catch (error) {
          console.error('免费机会刷新失败:', error)
          // 组件已卸载时不显示Toast
          
        }
      }, remainingSecondsToMidnight)
    }
    
    return () => {
      if (timer) {
        clearTimeout(timer)
      }
    }
  }, [remainingSecondsToMidnight, spinningWheelInfo, isSpinning])

  // 空函数 - 埋点已注释
  const onShow = () => {

    /* 调试 测试是否到了视口才上报埋点
    console.log('SpinningWheel onShow triggered', {
      time: new Date().toISOString(),
      winnerListLength: spinningWheelInfo.winnerList.length
    }) */
    
    xmlog.event(69116, 'slipPage', {
      currPage: 'welfareCenter',
      status: spinningWheelInfo.winnerList.length > 0 ? '1' : '0',
    })
  }
  const clickReport = ({
    id,
    module,
    currPage = CURRENT_PAGE,
  }: ReportProps) => {
    xmlog.click(id, module, currPage)
  }

  // 实物奖品记录点击埋点
  const prizeRecordClickReport = () => {
    clickReport({
      id: 69115,
      module: 'prizeRecord',
      currPage: {
        ...CURRENT_PAGE,
        Item: '实物奖品记录',
        status: spinningWheelInfo.winnerList.length > 0 ? '1' : '0',
      }
    })
  }


  // 刷新奖励池 - 观看广告刷新
  const handleRefreshReward = useThrottleCallback(async (shouldReport: boolean = true) => {

    try {
      setIsRefreshing(true)
      // 根据参数决定是否上报69115事件
      if (shouldReport) {
        clickReport({
          id: 69115, module: '', currPage: {
            ...CURRENT_PAGE,
            Item: '奖品换一换',
            status: spinningWheelInfo.winnerList.length > 0 ? '1' : '0',
          }
        })
      }

      // 1. 观看激励视频
      const res = await watchAd({
        sourceName: AD_SOURCE.SPINNING_WHEEL,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.SPINNING_WHEEL,
        coins: 0,
        rewardVideoStyle: 0,
        configPositionName: 'inspire_video_spinning_wheel_refresh_rn',
        extInfo: JSON.stringify({
          sourceName: AD_SOURCE.SPINNING_WHEEL,
          action: 'refresh'
        })
      })

      if (res.success) {
        // 2. 使用 rewardGoldCoin 方法刷新奖励，isNoCoin 设为 true
        const result = await rewardGoldCoin({
          rewardType: 2, // 刷新奖励类型
          sourceName: AD_SOURCE.SPINNING_WHEEL,
          coins: 0,
          adId: res.adId,
          adResponseId: res.adResponseId,
          encryptType: res.encryptType,
          ecpm: res.ecpm,
          fallbackReq: res.fallbackReq || FallbackReqType.NORMAL,
          extMap: JSON.stringify({
            sourceName: AD_SOURCE.SPINNING_WHEEL,
            action: 'refresh'
          }),
          isNoCoin: true // 告诉服务器用户已经刷新奖励，不需要发放金币
        }, true) // 使用自定义奖励提示

        if (result?.success) {
          // 3. 重新调用 queryRotaryTableInfo 接口获取奖励信息
          
          // 检查组件是否已卸载
          if (!isMountedRef.current) {
            return
          }
          
          try {
            await safeFetchSpinningWheel()
            
            // 再次检查组件是否已卸载
            if (isMountedRef.current) {
              Toast.info(result.toast || '刷新成功！')
            }
          } catch (error) {
            console.error('刷新转盘数据失败:', error)
            
          }

          // 刷新成功埋点已注释
        } else if (result?.retry) {
          // 需要重试
          setTimeout(() => {
            handleRefreshReward()
          }, 1000)
        } else {
          Toast.info('刷新奖励失败')
          // 刷新失败埋点已注释
        }
      }
    } catch (error) {
      console.error('刷新奖励失败:', error)
      Toast.info('刷新奖励失败，请稍后重试')

      if (onError) {
        onError({
          code: -1,
          message: '刷新奖励失败',
          source: 'api',
          details: error
        })
      }
    } finally {
      setIsRefreshing(false)
    }
  })



  // 九宫格抽奖动画 - 高亮格子序列
  const startGridAnimation = (targetPrizeIndex: number) => {
    setIsSpinning(true)
    setCurrentHighlightIndex(0)

    let currentIndex = 0
    let rounds = 0
    const totalRounds = 3 // 转3圈
    const maxRounds = totalRounds * 8 + targetPrizeIndex + 1
    const baseSpeed = 100 // 基础速度

    const animateStep = () => {
      if (rounds >= maxRounds) {
        // 动画结束，停在中奖位置
        setTimeout(() => {
          setCurrentHighlightIndex(-1)
          setIsSpinning(false)
          claimReward(targetPrizeIndex)
          updateWelfare()
        }, 500)
        return
      }

      setCurrentHighlightIndex(currentIndex)
      currentIndex = (currentIndex + 1) % 8
      rounds++

      // 动态调整速度，后面越来越慢
      const progress = rounds / maxRounds
      const speed = baseSpeed + progress * progress * 300

      setTimeout(animateStep, speed)
    }

    animateStep()
  }

  // 领取奖励 - 显示中奖结果
  const claimReward = async (targetPrizeIndex: number) => {
    try {
      // 获取中奖奖品信息
      const prize = spinningWheelInfo.prizes[targetPrizeIndex]
      if (prize) {
        // 显示奖励弹窗
        console.log('spinResultRef.current2', spinResultRef.current);
        setModalInfo({
          ...spinResultRef.current,
          isFirstFree: spinningWheelInfo.firstFree,
        })
        spinResultRef.current = initialState;

        // 更新状态 - 重新获取转盘信息
        if (spinningWheelInfo.firstFree) {
          safeFetchSpinningWheel()
        }
        // 抽奖结果埋点已注释
      }
    } catch (error) {
      console.error('领取奖励失败:', error)
      Toast.info('获取奖励失败，请稍后重试')

      if (onError) {
        onError({
          code: -1,
          message: '领取奖励失败',
          source: 'reward',
          details: error
        })
      }
    }
  }

  // 处理抽奖
  const handleSpin = async (shouldReport: boolean = true) => {
    if (disabled || isSpinning) return

    try {
      // 根据参数决定是否上报69115事件
      if (shouldReport) {
        clickReport({
          id: 69115, 
          module: '', 
          currPage: {
            ...CURRENT_PAGE,
            Item: spinningWheelInfo.buttonText,
            status: spinningWheelInfo.winnerList.length > 0 ? '1' : '0',
          }
        })
      }

      if (onSpinStart) {
        onSpinStart()
      }

      if (!spinningWheelInfo.firstFree && spinningWheelInfo.costCoins > 0) {
        // 检查金币余额
        if (balance.coins < spinningWheelInfo.costCoins) {
          Toast.info('金币不足，快去攒金币吧')
          return
        }
        
        // 扣除金币
        const { exchangeGoldCoin } = await import('services/welfare/detail')
        const exchangeResult = await exchangeGoldCoin({
          consumeType: CONSUME_TYPE,
          coins: spinningWheelInfo.costCoins,
          sourceName: 'SPINNING_WHEEL',
        })
        
        // 检查扣除金币是否成功
        if (!exchangeResult?.data?.success) {
          Toast.info(exchangeResult?.data?.toast || '扣除金币失败，请稍后重试')
          return
        }
      }

      // 调用新的抽奖API
      const { executeSpinningWheel } = await import('services/welfare/spiningWheel')
      const result = await executeSpinningWheel()

      if (result?.data?.success) {
        spinResultRef.current = {
          ...spinResultRef.current,
          ...result.data,
          showRewardModal: true,
          modalType: result.data.awardType === 0 ? 'thanks' : 'reward',
          isPhysicalPrize: result.data.awardType === 2 ? true : false,
          awardCode: result.data.awardCode,
          coins: result.data.coins,
        }
        console.log('result.data current', result.data);
        console.log('spinResultRef.current1', spinResultRef.current);
        // 使用服务端返回的中奖位置
        const targetPrizeIndex = result.data.awardIndex - 1 // API返回1-8，需要转换为0-7

        // 开始九宫格动画
        startGridAnimation(targetPrizeIndex)
      } else {
        Toast.info(result?.data?.toast || '抽奖失败，请稍后重试')

        if (onError) {
          onError({
            code: result?.data?.code || -1,
            message: result?.data?.toast || '抽奖失败',
            source: 'api',
            details: result
          })
        }

      }
    } catch (error) {
      console.error('抽奖失败:', error)
      Toast.info('抽奖失败，请稍后重试')

      if (onError) {
        onError({
          code: -1,
          message: '抽奖失败',
          source: 'spin',
          details: error,
        })
      }
    }
  }

  // 计算奖品位置 - 按照设计图中的3x3网格布局
  const getPrizePosition = (index: number) => {
    const positions = [
      { row: 0, col: 0 }, // 谢谢参与 - 左上
      { row: 0, col: 1 }, // 10金币 - 上中
      { row: 0, col: 2 }, // 20金币 - 右上
      { row: 1, col: 2 }, // 30金币 - 右中
      { row: 2, col: 2 }, // 50金币 - 右下
      { row: 2, col: 1 }, // 100金币 - 下中
      { row: 2, col: 0 }, // 500金币 - 左下
      { row: 1, col: 0 }, // 1000金币 - 左中
    ]

    const position = positions[index]
    const itemWidth = px(96)
    const itemHeight = px(96)
    const spacing = px(11)

    return {
      left: position.col * (itemWidth + spacing),
      top: position.row * (itemHeight + spacing),
      width: itemWidth,
      height: itemHeight,
    }
  }

  // 渲染奖品项
  const renderPrizeItem = (prize: any, index: number) => {
    // 添加安全检查，防止spinningWheelInfo为undefined时的解构错误
    if (!spinningWheelInfo) {
      
      return null;
    }

    const position = getPrizePosition(index)
    const isHighlighted = currentHighlightIndex === index
    const { firstFree } = spinningWheelInfo;
    const renderItem = (prize: any) => {
      // 添加安全检查，防止prize为undefined时的解构错误
      if (!prize) {
        
        return (
          <View
            style={{
              width: position.width,
              height: position.height,
              borderWidth: 0,
              borderRadius: px(6),
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: theme.gridBackground,
            }}
          >
            <Text style={{ fontSize: px(12), color: theme.titleColor }}>
              加载中...
            </Text>
          </View>
        );
      }

      const { amountText, icon, cashText, awardType } = prize;
      // 判断是否为双行奖品
      const isDoubleLine = awardType === 1 && !firstFree;
      
      return (
        <View
          style={{
            width: position.width,
            height: position.height,
            borderWidth: 0, // 移除边框
            borderRadius: px(6),
            alignItems: 'center',
            justifyContent: 'space-between',
            backgroundColor: isHighlighted ? theme.gridHighlightBackground : theme.gridBackground,
            //paddingVertical: px(8),
            //paddingHorizontal: px(4),
          }}
        >
          {/* 文本区域 */}
          <View style={{ 
            alignItems: 'center', 
            justifyContent: isDoubleLine ? 'flex-start' : 'center',
            flex: 1,
            height: px(32), // 确保文本区域有足够高度
            width: px(96),
            
          }}>
            <Text
              style={{
                fontSize: px(12),
                color: isHighlighted ? theme.titleHighlightColor : theme.titleColor,
                fontWeight: 'bold',
                textAlign: 'center',
                marginTop: isDoubleLine ? px(6) : px(11), // 所有文字都下移动5px
              }}
            >
              {((firstFree || awardType === 2 || awardType === 0) ? amountText : cashText)}
            </Text>
            {isDoubleLine && (
              <Text
                style={{
                  backgroundColor: 'rgba(255, 109, 109, .1)',
                  fontSize: px(9),
                  color: '#D87F7F',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  marginTop: px(1), // 第二行文字也下移动5px
                  paddingHorizontal: px(2),
                }}
              >
                {amountText}
              </Text>
            )}
          </View>

          {/* 金币图标 */}
          <Image
            source={{ uri: icon }}
            style={{
              width: position.width, // 减去左右padding
              height: px(64),
              //marginBottom: px(2),
              //backgroundColor: 'yellow',
              //bottom: px(0),
              
            }}
            resizeMode="contain"
          />
        </View>
      )
    }
    const currentPrize = spinningWheelInfo.prizes[index]
    const prevPrize = prevPrizes[index];
    return (
      <View
        key={currentPrize.id}
        style={[
          {
            position: 'absolute',
            left: position.left,
            top: position.top,
          },
        ]}
      >
        {/* 奖品金额文字在上方 */}
        <View style={{ position: 'absolute', opacity: flipping ? 0 : 1 }}>
          {renderItem(currentPrize)}
        </View>
        {flipping && (
          <Lattice
            frontSlot={renderItem(prevPrize)}
            backSlot={renderItem(currentPrize)}
            fire={flipping}
            duration={600}
          />
        )}
      </View>
    )
  }



  // 获取中心按钮文案
  const getCenterButtonText = () => {
    if (isSpinning) {
      return '抽奖中'
    }
    if (isRefreshing) {
      return '刷新中'
    }
     /* if (spinningWheelInfo.firstFree) {
       return '首次免费\n2333333金币'
     } */
    return spinningWheelInfo.buttonText
  }

  // 解析按钮文案，支持换行符分割 首次免费-10000金币这两个分割起来 实现上面没有下划线 下面有下划线
  const parseButtonText = (text: string) => {
    const lines = text.split('\n')
    return lines
  }

  // todo 添加按钮逻辑 处理弹窗的按钮
  const handleModalBtn = () => {
    // 添加安全检查，防止modalInfo为undefined时的解构错误
    if (!modalInfo) {
      
      return;
    }

    const { modalType, isFirstFree, isPhysicalPrize, showRewardModal } = modalInfo;
    if (isPhysicalPrize && modalType === 'reward') {
      navigation.navigate('PrizeInfoForm', {
        awardCode: modalInfo.awardCode,
        orderNo: modalInfo.orderNo
      });
      // Clipboard.setString(modalInfo.awardCode || '');
      // Toast.info('兑换码已复制')
      return
    }
    if (!isFirstFree) {
      // 从弹窗触发时，不上报69115事件
      handleSpin(false);
      return;
    }
  }



  // 是否需要刷新
  const timeTick = useCallback(() => {
    const now = Date.now()
    const flag = now - lastRefreshTime > 5 * 60 * 1000
    return flag && autoRefresh
  }, [lastRefreshTime, autoRefresh])

  const handleReflesh = async () => {
    // if (!timeTick()) {
    //   Toast.info('刷新太频繁了，请稍后试试吧')
    //   return
    // }
    if (isSpinning) {
      return
    }
    setRefleshFlag(refleshFlag + 1)
    // 从弹窗触发时，不上报69115事件
    await handleRefreshReward(false)
    updateRefreshTime()
    setFlipping(true)
    setTimeout(() => {
      setFlipping(false)
    }, 700)
  }

  // 创建一个新的函数用于转盘底部的奖品换一换按钮
  const handleRefleshFromWheel = async () => {
    if (isSpinning) {
      return
    }
    setRefleshFlag(refleshFlag + 1)
    // 从转盘底部触发时，上报69115事件
    await handleRefreshReward(true)
    updateRefreshTime()
    setFlipping(true)
    setTimeout(() => {
      setFlipping(false)
    }, 700)
  }

  // 更严格的条件渲染检查

  if (!spinningWheelInfo.success) {
    console.log('转盘组件: success 为 false');
    return null;
  }
  
  

  // 方案三：更严格的条件渲染
  const shouldShowMarquee = spinningWheelInfo?.winnerList && 
                           Array.isArray(spinningWheelInfo.winnerList) &&
                           spinningWheelInfo.winnerList.length >= 10 &&
                           spinningWheelInfo.success

  return (
    <ScrollAnalyticComp
      itemKey={'SpinningWheel'}
      onShow={onShow}
    >
      <View 
        ref={spinningWheelRef}
        onLayout={() => {
          setIsLayoutReady(true);
        }}
      >
        <ModuleCard
          style={{
            ...styles.container,
          }}
        >
        {/* 标题区域 - 左右布局 */}
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: px(0),
            marginBottom: px(4),
          }}
        >
          {/* 左侧标题容器 */}
          <View style={{ flex: 1, alignItems: 'flex-start' }}>
            <Text style={{ fontSize: px(16), fontWeight: 'bold', color: theme.headerTextColor }}>
              {spinningWheelInfo.title || '幸运大转盘'}
            </Text>
          </View>

          {/* 右侧跑马灯容器 - 右对齐，与标题保持8px间距 */}
          <View style={{ 
            width: px(208), 
            alignItems: 'flex-end',
            //marginLeft: px(8), // 与标题保持8px间距
            
          }}>
            {shouldShowMarquee && (
              <WinnerMarquee winnerList={spinningWheelInfo.winnerList} />
            )}
          </View>
        </View>

        {/* 实物奖品记录链接 */}
        <TouchableOpacity
          style={{
            alignSelf: 'flex-start',
            marginBottom: px(12-11),
            //marginLeft: px(11 + 4), /
            flexDirection: 'row',
            alignItems: 'center',
          }}
          onPress={() => {
            prizeRecordClickReport()
            navigation.navigate('RewardRecord')
          }}
          activeOpacity={0.6}
        >
          <Text
            style={{
              fontSize: px(11),
              color: theme.recordTextColor,
              fontWeight: '400',
              // textDecorationLine: 'underline',
            }}
          >
            实物奖品记录
          </Text>
          <Image
            source={{ uri: PRIZE_NAV_PIC }}
            style={{
              width: px(6),
              height: px(12),
              marginLeft: px(2),
              marginTop: px(1),
            }}
            resizeMode="contain"
          />
        </TouchableOpacity>

        {/* 九宫格区域 */}
        <View style={{ alignItems: 'center' }}>
          {/* 九宫格奖品容器 */}
          <View
            style={{
              width: px(310), // 310 - 2 * 11 = 288
              height: px(310), // 310 - 2 * 11 = 288
              position: 'relative',
              margin: px(11),
            }}
          >
            {/* 奖品项 */}
            {spinningWheelInfo.prizes?.map((prize, index) => {
              return renderPrizeItem(prize, index)
            })}
          </View>

          {/* 中心按钮 */}
          <View
            style={{
              position: 'absolute',
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10,
            }}
          >
            {/* 指引手动画 - 位于按钮右下角 */}
            {renderGuideHand()}
            <TouchableOpacity
              style={{
                width: px(96),
                height: px(96),
                borderRadius: px(6),
                backgroundColor: 'transparent',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onPress={() => {
                handleSpin()
                // 点击后隐藏指引手
                setShowGuideHand(false)
              }}
              disabled={isSpinning || isRefreshing}
              activeOpacity={0.8}
            >
              {/* 按钮背景图片 */}
              <Image
                source={{ uri: 'https://imagev2.xmcdn.com/storages/80dc-audiofreehighqps/09/BD/GKwRIJIMJdACAAAg9QPJ0CEk.png' }}
                style={{
                  position: 'absolute',
                  width: px(96),
                  height: px(96),
                  borderRadius: px(6),
                }}
                resizeMode="cover"
              />
              {/* 按钮文字 */}
              {spinningWheelInfo.firstFree ? (
                (() => {
                  const buttonText = getCenterButtonText()
                  const lines = parseButtonText(buttonText)
                  return (
                    <View style={{ alignItems: 'center', zIndex: 1 }}>
                      <Text
                        style={{
                          fontSize: px(15),
                          color: theme.buttonTextColor ,
                          fontWeight: '600',
                          textAlign: 'center',
                          lineHeight: px(16),
                        }}
                      >
                        {lines[0]}
                      </Text>
                      {lines[1] && (
                        <Text
                          style={{
                            fontSize: px(10),
                            color:  theme.buttonTextColor ,
                            fontWeight: '600',
                            textAlign: 'center',
                            lineHeight: px(16),
                            opacity: 0.6,
                            textDecorationLine: 'line-through', // 第二行添加删除线
                            
                          }}
                        >
                          {lines[1]}
                        </Text>
                      )}
                    </View>
                  )
                })()
              ) : (
                // 非免费抽奖时使用原来的渲染方式
                <Text
                  style={{
                    fontSize: px(12),
                    color: theme.buttonTextColor,
                    fontWeight: '600',
                    textAlign: 'center',
                    lineHeight: px(16),
                    zIndex: 1,
                  }}
                >
                  {getCenterButtonText()}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* 底部提示文案 */}
        {!spinningWheelInfo.firstFree ? (
          <TouchableOpacity
            style={{
              width: px(311),
              height: px(44),
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              paddingVertical: px(8),
              borderRadius: px(6),
              backgroundColor: finalSpinningWheelTheme.bottomTipsBackgroundColorAD,
              marginTop: px(14-11),
              alignSelf: 'center',
            }}
            onPress={handleRefleshFromWheel}  // 使用新的函数 大转盘主界面的奖品换一换按钮
            disabled={isRefreshing}
            activeOpacity={0.8}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Image source={{ uri: globalTheme === 'dark' ? ROTATE_ICON_DARK : ROTATE_ICON }} style={{ width: px(12), height: px(12) }} />
              <Text
                style={{
                  fontSize: px(12),
                  fontWeight: '500',
                  textAlign: 'center',
                  color: finalSpinningWheelTheme.bottomTipsTextColorAD,
                }}
              >
                {'看广告换一换奖品'}
              </Text>
            </View>
            
              <Text
                style={{
                  fontSize: px(8),
                  fontWeight: '300',
                  textAlign: 'center',
                  color: finalSpinningWheelTheme!.bottomTipsTextColorAD,
                  marginTop: px(2),
                }}
              >
                {spinningWheelInfo.bottomTips?.tipsText}
              </Text>
          </TouchableOpacity>
        ) : (
          <Text
            style={{
              backgroundColor: finalSpinningWheelTheme.bottomTipsBackgroundColorFree,
              fontSize: px(12),
              color: finalSpinningWheelTheme.bottomTipsTextColorFree,
              textAlign: 'center',
              marginTop: px(14-11),
              width: px(311),
              height: px(33),
              borderRadius: px(6),
              lineHeight: px(33),
            }}
          >
            {spinningWheelInfo.bottomTips?.tipsText}
          </Text>
        )}
        </ModuleCard>
      </View>

      
      {/* 弹窗· */}
      <SpinningWheelModal
        visible={false}
        onClose={() => { }}
        onPress={handleModalBtn}
        onReflesh={handleReflesh}
      />
    </ScrollAnalyticComp>
  )
}

export default SpiningWheel
