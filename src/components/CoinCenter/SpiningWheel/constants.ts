// SpiningWheel组件常量定义

// 转盘配置
export const WHEEL_CONFIG = {
  // 转盘大小
  WHEEL_SIZE: 280,
  WHEEL_RADIUS: 140,
  
  // 奖品数量
  PRIZE_COUNT: 8,
  
  // 每个奖品扇形角度 (360度 / 8个奖品)
  PRIZE_ANGLE: 45,
  
  // 中心按钮大小
  CENTER_BUTTON_SIZE: 80,
  CENTER_BUTTON_RADIUS: 40,
  
  // 奖品区域大小
  PRIZE_ITEM_SIZE: 70,
  
  // 奖品图标大小
  PRIZE_ICON_SIZE: 24,
  
  // 指针大小
  POINTER_SIZE: 20,
}

// 动画配置
export const ANIMATION_CONFIG = {
  // 旋转动画时长 (毫秒)
  SPIN_DURATION: 3000,
  
  // 最小旋转圈数
  MIN_ROTATIONS: 3,
  
  // 最大旋转圈数
  MAX_ROTATIONS: 5,
  
  // 缓动函数
  EASING: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  
  // 结果显示延迟
  RESULT_DELAY: 500,
}

// 奖品位置配置 (8个奖品的角度位置)
export const PRIZE_POSITIONS = [
  { id: 1, angle: 0, x: 140, y: 35 },     // 12点方向
  { id: 2, angle: 45, x: 210, y: 70 },    // 1:30方向
  { id: 3, angle: 90, x: 245, y: 140 },   // 3点方向
  { id: 4, angle: 135, x: 210, y: 210 },  // 4:30方向
  { id: 5, angle: 180, x: 140, y: 245 },  // 6点方向
  { id: 6, angle: 225, x: 70, y: 210 },   // 7:30方向
  { id: 7, angle: 270, x: 35, y: 140 },   // 9点方向
  { id: 8, angle: 315, x: 70, y: 70 },    // 10:30方向
]

// 奖品背景颜色 (8个不同颜色)
export const PRIZE_COLORS = [
  '#FFE5E5', // 浅红色
  '#E5F3FF', // 浅蓝色
  '#E5FFE5', // 浅绿色
  '#FFF5E5', // 浅橙色
  '#F0E5FF', // 浅紫色
  '#FFE5F5', // 浅粉色
  '#E5FFFF', // 浅青色
  '#FFFEE5', // 浅黄色
]

// 深色模式奖品背景颜色
export const PRIZE_COLORS_DARK = [
  '#4A2C2C', // 深红色
  '#2C3A4A', // 深蓝色
  '#2C4A2C', // 深绿色
  '#4A3A2C', // 深橙色
  '#3A2C4A', // 深紫色
  '#4A2C3A', // 深粉色
  '#2C4A4A', // 深青色
  '#4A4A2C', // 深黄色
]

// 默认奖品数据 (用于测试和占位)
export const DEFAULT_PRIZES = [
  { id: 1, amountText: '5金币', icon: '', awardType: 1, fallback: true },
  { id: 2, amountText: '10金币', icon: '', awardType: 1 },
  { id: 3, amountText: '20金币', icon: '', awardType: 1 },
  { id: 4, amountText: '30金币', icon: '', awardType: 1 },
  { id: 5, amountText: '50金币', icon: '', awardType: 1 },
  { id: 6, amountText: '100金币', icon: '', awardType: 1 },
  { id: 7, amountText: '500金币', icon: '', awardType: 1 },
  { id: 8, amountText: '1000金币', icon: '', awardType: 1 },
]

// API端点
export const API_ENDPOINTS = {
  QUERY_ROTARY_TABLE: '/incentive/ting/welfare/queryRotaryTableInfo',
  DRAW_ROTARY_TABLE: '/incentive/ting/welfare/drawRotaryTable',
}

// 错误码定义
export const ERROR_CODES = {
  NETWORK_ERROR: 1001,
  API_ERROR: 1002,
  AD_ERROR: 1003,
  ANIMATION_ERROR: 1004,
  INVALID_RESPONSE: 1005,
  NO_CHANCES: 1006,
}

// 错误消息
export const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.API_ERROR]: '服务器异常，请稍后重试',
  [ERROR_CODES.AD_ERROR]: '广告加载失败，请稍后重试',
  [ERROR_CODES.ANIMATION_ERROR]: '动画播放异常',
  [ERROR_CODES.INVALID_RESPONSE]: '数据格式异常',
  [ERROR_CODES.NO_CHANCES]: '今日抽奖次数已用完',
}

// 埋点事件码
export const ANALYTICS_EVENTS = {
  WHEEL_SHOW: 67701,
  WHEEL_SPIN: 67702,
  WHEEL_CLICK: 67703,
  WHEEL_RESULT: 67704,
}

// 广告配置
export const AD_CONFIG = {
  SOURCE_NAME: 'SPINNING_WHEEL',
  POSITION_NAME: 'spinning_wheel_position',
  SLOT_ID: 'spinning_wheel_slot',
  REWARD_TYPE: 'SPINNING_WHEEL',
} 