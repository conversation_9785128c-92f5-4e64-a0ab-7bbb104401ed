// SpiningWheel组件样式定义

import { StyleSheet } from 'react-native'
import { px } from 'utils/px'
import { WheelTheme } from './types'
import { WHEEL_CONFIG } from './constants'

export const getStyles = (theme: WheelTheme) => StyleSheet.create({
  // 主容器样式
  container: {
    alignItems: 'center',
    paddingTop: px(18),
    paddingBottom: px(16),
    paddingHorizontal: px(16),
  },

  // 头部信息区域
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: px(20),
  },

  headerLeft: {
    flex: 1,
    maxWidth: '70%',
  },

  title: {
    fontSize: px(18),
    fontWeight: '600',
    color: theme.prizeTextColor,
    marginBottom: px(4),
  },

  subTitle: {
    fontSize: px(14),
    color: theme.prizeTextColor,
    opacity: 0.7,
    lineHeight: px(20),
  },

  headerRight: {
    alignItems: 'flex-end',
  },

  // 转盘容器样式
  wheelContainer: {
    width: px(theme.wheelSize),
    height: px(theme.wheelSize),
    borderRadius: px(theme.wheelSize / 2),
    backgroundColor: theme.wheelBackground,
    borderWidth: px(2),
    borderColor: theme.wheelBorder,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },

  // 转盘旋转容器
  wheelRotateContainer: {
    width: px(theme.wheelSize),
    height: px(theme.wheelSize),
    position: 'absolute',
  },

  // 奖品区域样式
  prizeSection: {
    position: 'absolute',
    width: px(theme.wheelSize),
    height: px(theme.wheelSize),
    borderRadius: px(theme.wheelSize / 2),
    overflow: 'hidden',
  },

  // 单个奖品项样式
  prizeItem: {
    position: 'absolute',
    width: px(WHEEL_CONFIG.PRIZE_ITEM_SIZE),
    height: px(WHEEL_CONFIG.PRIZE_ITEM_SIZE),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: px(8),
  },

  prizeIcon: {
    width: px(WHEEL_CONFIG.PRIZE_ICON_SIZE),
    height: px(WHEEL_CONFIG.PRIZE_ICON_SIZE),
    marginBottom: px(4),
  },

  prizeText: {
    fontSize: px(theme.prizeFontSize),
    color: theme.prizeTextColor,
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: px(16),
  },

  // 中心按钮样式
  centerButton: {
    width: px(WHEEL_CONFIG.CENTER_BUTTON_SIZE),
    height: px(WHEEL_CONFIG.CENTER_BUTTON_SIZE),
    borderRadius: px(WHEEL_CONFIG.CENTER_BUTTON_RADIUS),
    backgroundColor: theme.buttonBackground,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  centerButtonDisabled: {
    backgroundColor: theme.buttonDisabledBackground,
  },

  centerButtonText: {
    fontSize: px(12),
    color: theme.buttonTextColor,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: px(16),
  },

  centerButtonTextDisabled: {
    color: '#999999',
  },

  // 指针样式
  pointer: {
    position: 'absolute',
    top: px(-10),
    width: 0,
    height: 0,
    borderLeftWidth: px(theme.pointerSize / 2),
    borderRightWidth: px(theme.pointerSize / 2),
    borderBottomWidth: px(theme.pointerSize),
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: theme.pointerColor,
    zIndex: 5,
  },

  // 分割线样式
  divider: {
    position: 'absolute',
    width: px(1),
    height: px(theme.wheelSize / 2),
    backgroundColor: theme.wheelBorder,
    opacity: 0.3,
  },

  // 加载状态样式
  loadingContainer: {
    width: px(theme.wheelSize),
    height: px(theme.wheelSize),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.wheelBackground,
    borderRadius: px(theme.wheelSize / 2),
    borderWidth: px(2),
    borderColor: theme.wheelBorder,
  },

  loadingText: {
    fontSize: px(16),
    color: theme.prizeTextColor,
    marginTop: px(10),
  },

  // 错误状态样式
  errorContainer: {
    width: px(theme.wheelSize),
    height: px(theme.wheelSize),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.wheelBackground,
    borderRadius: px(theme.wheelSize / 2),
    borderWidth: px(2),
    borderColor: '#FF4444',
  },

  errorText: {
    fontSize: px(14),
    color: '#FF4444',
    textAlign: 'center',
    marginTop: px(10),
    paddingHorizontal: px(20),
  },

  retryButton: {
    marginTop: px(15),
    paddingHorizontal: px(20),
    paddingVertical: px(8),
    backgroundColor: theme.buttonBackground,
    borderRadius: px(20),
  },

  retryButtonText: {
    fontSize: px(12),
    color: theme.buttonTextColor,
    fontWeight: '500',
  },

  // 结果弹窗样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  modalContainer: {
    width: px(300),
    backgroundColor: theme.wheelBackground,
    borderRadius: px(16),
    padding: px(24),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },

  modalTitle: {
    fontSize: px(20),
    fontWeight: '600',
    color: theme.prizeTextColor,
    marginBottom: px(16),
  },

  modalPrizeIcon: {
    width: px(60),
    height: px(60),
    marginBottom: px(16),
  },

  modalPrizeAmount: {
    fontSize: px(24),
    fontWeight: '700',
    color: theme.buttonBackground,
    marginBottom: px(8),
  },

  modalMessage: {
    fontSize: px(14),
    color: theme.prizeTextColor,
    textAlign: 'center',
    marginBottom: px(24),
    opacity: 0.8,
  },

  modalButton: {
    width: '100%',
    height: px(44),
    backgroundColor: theme.buttonBackground,
    borderRadius: px(22),
    alignItems: 'center',
    justifyContent: 'center',
  },

  modalButtonText: {
    fontSize: px(16),
    color: theme.buttonTextColor,
    fontWeight: '600',
  },

  // 广告按钮样式
  adButton: {
    marginTop: px(16),
    paddingHorizontal: px(24),
    paddingVertical: px(12),
    backgroundColor: '#4CAF50',
    borderRadius: px(24),
    flexDirection: 'row',
    alignItems: 'center',
  },

  adButtonIcon: {
    width: px(16),
    height: px(16),
    marginRight: px(8),
    tintColor: '#FFFFFF',
  },

  adButtonText: {
    fontSize: px(14),
    color: '#FFFFFF',
    fontWeight: '600',
  },

  // 剩余次数显示
  remainingChances: {
    marginTop: px(16),
    paddingHorizontal: px(16),
    paddingVertical: px(8),
    backgroundColor: theme.wheelBorder,
    borderRadius: px(16),
  },

  remainingChancesText: {
    fontSize: px(12),
    color: theme.prizeTextColor,
    opacity: 0.8,
  },

  // 动画相关样式
  spinningIndicator: {
    position: 'absolute',
    top: px(-30),
    alignSelf: 'center',
    zIndex: 15,
  },

  spinningText: {
    fontSize: px(12),
    color: theme.buttonBackground,
    fontWeight: '600',
    textAlign: 'center',
  },

  // 奖品高亮样式
  prizeHighlight: {
    borderWidth: px(2),
    borderColor: theme.buttonBackground,
    shadowColor: theme.buttonBackground,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 8,
  },

  // 禁用状态样式
  disabledText: {
    fontSize: px(14),
    color: theme.buttonDisabledBackground,
    fontWeight: '500',
  },

  // 小屏幕适配样式
  smallWheelContainer: {
    width: px(240),
    height: px(240),
    borderRadius: px(120),
    backgroundColor: theme.wheelBackground,
    borderWidth: px(2),
    borderColor: theme.wheelBorder,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },

  smallCenterButton: {
    width: px(60),
    height: px(60),
    borderRadius: px(30),
    backgroundColor: theme.buttonBackground,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  smallPrizeItem: {
    position: 'absolute',
    width: px(50),
    height: px(50),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: px(8),
  },

  smallPrizeIcon: {
    width: px(20),
    height: px(20),
    marginBottom: px(2),
  },

  smallPrizeText: {
    fontSize: px(10),
    color: theme.prizeTextColor,
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: px(12),
  },

  // 辅助功能样式
  accessibilityLabel: {
    position: 'absolute',
    left: px(-1000),
    top: px(-1000),
    width: px(1),
    height: px(1),
    opacity: 0,
  },
})

// 动态样式生成函数
export const getDynamicStyles = (theme: WheelTheme, screenWidth: number) => {
  const isSmallScreen = screenWidth < 375
  const baseStyles = getStyles(theme)
  
  if (isSmallScreen) {
    return {
      ...baseStyles,
      wheelContainer: baseStyles.smallWheelContainer,
      centerButton: baseStyles.smallCenterButton,
      prizeItem: baseStyles.smallPrizeItem,
      prizeIcon: baseStyles.smallPrizeIcon,
      prizeText: baseStyles.smallPrizeText,
    }
  }
  
  return baseStyles
}

// 奖品位置计算样式
export const getPrizeItemStyle = (
  theme: WheelTheme, 
  index: number, 
  backgroundColor: string,
  isHighlighted: boolean = false
) => {
  const angle = (index * WHEEL_CONFIG.PRIZE_ANGLE - 90) * (Math.PI / 180)
  const radius = (theme.wheelSize / 2) * 0.7
  const centerX = theme.wheelSize / 2
  const centerY = theme.wheelSize / 2
  
  const x = centerX + radius * Math.cos(angle) - WHEEL_CONFIG.PRIZE_ITEM_SIZE / 2
  const y = centerY + radius * Math.sin(angle) - WHEEL_CONFIG.PRIZE_ITEM_SIZE / 2
  
  return {
    position: 'absolute' as const,
    left: px(x),
    top: px(y),
    width: px(WHEEL_CONFIG.PRIZE_ITEM_SIZE),
    height: px(WHEEL_CONFIG.PRIZE_ITEM_SIZE),
    backgroundColor,
    borderRadius: px(8),
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    ...(isHighlighted && {
      borderWidth: px(2),
      borderColor: theme.buttonBackground,
      shadowColor: theme.buttonBackground,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.5,
      shadowRadius: 8,
      elevation: 8,
    }),
  }
}

// 分割线样式生成
export const getDividerStyle = (theme: WheelTheme, index: number) => {
  const angle = index * WHEEL_CONFIG.PRIZE_ANGLE
  
  return {
    position: 'absolute' as const,
    width: px(1),
    height: px(theme.wheelSize / 2),
    backgroundColor: theme.wheelBorder,
    opacity: 0.3,
    left: px(theme.wheelSize / 2),
    top: px(0),
    transform: [{ rotate: `${angle}deg` }],
  }
} 