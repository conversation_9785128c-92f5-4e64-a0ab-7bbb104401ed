// 中奖跑马灯组件
import React, { useRef, useState, useCallback, useEffect } from 'react'
import { View, Text, Animated, Image, Easing } from 'react-native'
import { px } from 'utils/px'
import xmlog from 'utilsV2/xmlog'
import { Winner } from './types'
import { themeAtom } from 'atom/theme'
import { useAtomValue } from 'jotai'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'


interface WinnerMarqueeProps {
  winnerList: Winner[]
  style?: any
}

const WinnerMarquee: React.FC<WinnerMarqueeProps> = ({ winnerList, style }) => {
  const scrollY = useRef(new Animated.Value(0)).current
  const [containerHeight, setContainerHeight] = useState(0)
  const animationRef = useRef<Animated.CompositeAnimation | null>(null)
  const theme = useAtomValue(themeAtom)
  const [count, setCount] = useState(0)

  // useEffect(() => {
  //   const listener = scrollY.addListener(({ value }) => {
  //     console.log('scrollY value:', value)
  //   })

  //   return () => {
  //     scrollY.removeListener(listener)
  //   }
  // }, [scrollY])

  // 默认头像
  const defaultAvatar = 'https://imagev2.xmcdn.com/storages/6619-audiofreehighqps/74/1F/GAqh9sAMT-UmAABf3gPk8Q9g.png'

  // 处理中奖数据
  const processWinnerData = useCallback((winners: Winner[]) => {
    // 添加安全检查，防止winners为undefined时的错误
    if (!winners || !Array.isArray(winners)) {
      
      return [];
    }
    
    return winners.map(winner => ({
      ...winner,
      // 直接展示原始用户名，不做脱敏处理
      displayName: winner.name,
      // 格式化奖励文案 - 奖品内容需完整展示
      displayAward: winner.award
    }))
  }, [])

  // 启动滚动动画
  const startScrolling = useCallback(() => {
    if (winnerList.length <= 1 || containerHeight <= 0) return
    
    // 记录滚动次数
    setCount(count + 1)
    const itemHeight = px(16) // 单个项目高度
    const totalHeight = winnerList.length * itemHeight
    
    // 从容器高度开始，滚动到负的总高度
    scrollY.setValue(0)
    
    animationRef.current = Animated.loop(
      Animated.sequence([
        Animated.timing(scrollY, {
          toValue: -totalHeight,
          duration: winnerList.length * 2000, // 每个项目显示2秒
          useNativeDriver: true,
          easing: Easing.linear,
        }),
        Animated.timing(scrollY, {
          toValue: 0,
          duration: 0, // 瞬间重置到顶部
          useNativeDriver: true,
        })
      ])
    )
    
    animationRef.current.start()
  }, [winnerList.length, containerHeight, scrollY])

  // 停止滚动动画
  const stopScrolling = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.stop()
      animationRef.current = null
    }
  }, [])

  // 当数据或尺寸变化时重新启动动画
  useEffect(() => {
    if (winnerList.length > 0 && containerHeight > 0) {
      stopScrolling()
      setTimeout(() => {
        startScrolling()
      }, 1000) // 延迟1秒开始滚动
    }
    
    return () => {
      stopScrolling()
    }
  }, [winnerList, containerHeight, startScrolling, stopScrolling])

  // 跑马灯展示埋点 
  const marqueeShowReport = useCallback((winnerCount: number) => {
    xmlog.event(69116, 'winnerMarquee', {
      currPage: 'welfareCenter',
      isDuplicateView: count === 0 ? '0' : '1',
      status: '1',
    })
  }, [])

  // 新增：曝光回调函数
  const onShow = useCallback(() => {
    if (winnerList?.length > 0) {
      marqueeShowReport(count)
    }
  }, [winnerList?.length, marqueeShowReport, count])

  // 跑马灯点击埋点
  const marqueeClickReport = useCallback((winner: Winner) => {
    // xmlog.click(67707, 'WinnerMarquee', {
    //   currPage: 'welfareCenter',
    //   winnerName: winner.name,
    //   award: winner.award
    // })
  }, [])

  // 组件挂载时发送展示埋点 此乃错误
  // useEffect(() => {
  //   if (winnerList?.length > 0) {
  //     marqueeShowReport(count)
  //   }
  // }, [winnerList?.length, marqueeShowReport, count])

  // 渲染中奖信息
  const renderWinnerItem = useCallback((winner: Winner, index: number) => {
    // 添加安全检查，防止winner为undefined时的错误
    if (!winner) {
      console.warn('winner is undefined for index:', index);
      return null;
    }
    
    const processedWinner = processWinnerData([winner])[0]
    
    // 添加安全检查，防止processedWinner为undefined时的错误
    if (!processedWinner) {
      console.warn('processedWinner is undefined for index:', index);
      return null;
    }
    
    return (
      <View key={index} style={styles.winnerItem}>
        <Image 
          source={{ uri: winner.avatar || defaultAvatar }} 
          style={styles.avatar}
        />
        <Text 
          style={[styles.winnerText, { color: theme === 'light' ? '#999999' : '#8D8D91' }]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {processedWinner.displayName}
          <Text style={{ color: '#FF4444'}}>{processedWinner.displayAward}</Text>
        </Text>
      </View>
    )
  }, [processWinnerData])

  // 方案三：条件渲染 - 只有有数据时才渲染
  if (!winnerList || winnerList.length === 0) {
    return null
  }

  return (
    <ScrollAnalyticComp
      itemKey={'WinnerMarquee'}
      onShow={onShow}
      
    >
      <View 
        style={[styles.container, style]}
        onLayout={(e) => setContainerHeight(e.nativeEvent.layout.height)}
      >
        <Animated.View
          style={[
            styles.scrollContent,
            { transform: [{ translateY: scrollY }] }
          ]}
        >
          
          {winnerList?.map(renderWinnerItem)}
          {winnerList?.[0] && renderWinnerItem(winnerList[0], 0)}
        </Animated.View>
      </View>
    </ScrollAnalyticComp>
  )
}

// 跑马灯样式
const styles = {
  container: {
    width: px(200), // 增加宽度，确保有足够空间
    height: px(20), // 单行显示的高度
    overflow: 'hidden' as const,
    // backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: px(0),
    paddingHorizontal: px(0),
    paddingVertical: px(2), // 减少内边距
    alignItems: 'flex-end' as const, // 右对齐
    justifyContent: 'flex-start' as const,
  },
  
  scrollContent: {
    flexDirection: 'column' as const,
  },
  
  winnerItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    width: '100%',
    height: px(16), // 固定每个项目高度
    paddingVertical: px(0),
    overflow: 'hidden' as const,
    justifyContent: 'flex-end' as const, // 右对齐
  },
  
  avatar: {
    width: px(12),
    height: px(12),
    borderRadius: px(6),
    marginRight: px(3),
  },
  
  winnerText: {
    fontSize: px(10),
    color: '#999999',
    fontWeight: '500' as const,
    flex: 0, // 移除flex，让文字紧贴头像
    numberOfLines: 1,
    flexWrap: 'nowrap' as const,
    textAlign: 'right' as const, // 文本右对齐
  }
}

export default WinnerMarquee 