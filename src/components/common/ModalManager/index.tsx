import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useAtomValue, useSetAtom } from 'jotai';
import { currentModalAtom } from './store';
import { registerModalsAtom } from './store';
import NotificationPopup from 'components/CoinCenter/NotificationPopup';
import AdPopWindow from 'components/CoinCenter/AdPopWindow';
import ShareCoinsRewardModal from 'components/CoinCenter/ShareCoins/ShareCoinsRewardModal';
import ShareCoinsModal from 'components/CoinCenter/ShareCoinsModal';
import NewbieGiftModal from 'components/CoinCenter/NewbieGiftModal';
import { commonConfigAtom } from "../../../pages/CoinCenter/store/commonConfig";
import type { ModalItem } from './types';
import { reportShowModal } from 'services/welfare';

const ModalManager: React.FC = () => {
    const currentModal = useAtomValue(currentModalAtom);
    const setCurrentModal = useSetAtom(currentModalAtom);
    // const [visible, setVisible] = useState(!!currentModal);
    const registerModals = useSetAtom(registerModalsAtom);
    const commonConfig = useAtomValue(commonConfigAtom);
    const popModalConfig = commonConfig.popModalConfig;

    const onShow = (id: string, traceId: number) => {
        console.log('------- onShow -------->', id, traceId);
        reportShowModal(id, traceId);
        return true;
    }

    const modalConfigs = [
        { id: 'NotificationPopup', priority: 1, Component: NotificationPopup, enable: false},
        { id: 'AdPopWindow', priority: 2, Component: AdPopWindow, enable: false },
        { id: 'ShareCoinsRewardModal', priority: 3, Component: ShareCoinsRewardModal, enable: false },
        { id: 'ShareCoinsModal', priority: 4, Component: ShareCoinsModal, enable: false },
        // 新人大礼包弹窗，默认不开启，完全由后端 popModalConfig 控制开启与优先级
        { id: 'NewbieGiftModal', priority: 5, Component: NewbieGiftModal, enable: false },
      ];

    // 用 useMemo 包裹 modals，消除依赖警告
    const modals = useMemo<Record<string, ModalItem>>(
        () =>
          modalConfigs.reduce((acc, { id, priority, Component, enable }) => {
            acc[id] = {
              id,
              priority,
              content: (
                <Component onShow={(traceId: number) => onShow(id, traceId)} />
              ),
              enable,
            };
            return acc;
          }, {} as Record<string, ModalItem>),
        []
      );


    useEffect(() => {
        if (commonConfig.loading) {
          console.log('------- commonConfig.loading -------->');
          setCurrentModal(null);
          // 如果配置正在加载，则清空弹窗队列,避免使用旧的弹窗配置
          registerModals([]);
          return;
        }

        if (!popModalConfig || !Array.isArray(popModalConfig.modalConfig)) {
          registerModals([]);
          return;
        };
        
        const modalList = popModalConfig.modalConfig
          .filter(cfg => cfg.enable && modals[cfg.id])
          .map(cfg => ({
            ...modals[cfg.id],
            priority: cfg.priority, // 使用配置中的 priority
            enable: cfg.enable,
          }));
        
        console.log('------- modalList -------->', modalList, popModalConfig.modalConfig);

        // 注册弹窗，即使没有弹窗，也注册一下，清空弹窗队列
        registerModals(modalList);

      }, [popModalConfig, commonConfig.loading, modals, registerModals]);

    // 当 currentModal 变化时，重置可见性
    // useEffect(() => {
    //   if (commonConfig.loading) {
    //     setCurrentModal(null);
    //   }
    // }, [currentModal, commonConfig.loading]);

    if (!currentModal) return null;

    // 判断 content 是函数还是 ReactNode
    const content =
        typeof currentModal.content === 'function'
        ? (currentModal.content as () => React.ReactNode)()
        : currentModal.content;

    console.log('------- showModalManager -------->', currentModal.id, content != null);

    return content;
};

export default ModalManager;
