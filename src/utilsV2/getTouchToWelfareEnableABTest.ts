import { NativeModules } from 'react-native';

/**
 * 检查 touchToWelfareEnable AB 测试配置
 * 决定是否显示 ListenTask 模块
 *
 * 检查逻辑：
 * 1. 使用 ADABTest.touchToWelfareEnable 属性
 * 2. 如果模块不可用，默认返回 true
 *
 * @returns Promise<boolean> 是否应该显示 ListenTask 模块
 */
export const getTouchToWelfareEnableABTest = async (): Promise<boolean> => {
  try {
    console.info('debug_touch_welfare_abtest_modules', '检查 ADABTest 模块', {
      ADABTest: !!NativeModules?.ADABTest,
      touchToWelfareEnable: NativeModules?.ADABTest?.touchToWelfareEnable,
      allABModules: Object.keys(NativeModules).filter(key =>
        key.includes('AB') || key.includes('Test')
      )
    });

    // 检查 ADABTest 模块的 touchToWelfareEnable 属性
    if (NativeModules?.ADABTest?.touchToWelfareEnable !== undefined) {
      const enableValue = NativeModules.ADABTest.touchToWelfareEnable;
      console.info('debug_touch_welfare_abtest', '使用 ADABTest.touchToWelfareEnable', {
        value: enableValue,
        type: typeof enableValue,
        result: !!enableValue
      });
      return !!enableValue;
    }

    // ADABTest 模块不可用时的默认行为
    console.warn('debug_touch_welfare_abtest_unavailable', 'ADABTest 模块不可用，默认显示 ListenTask', {
      availableModules: Object.keys(NativeModules)
    });
    return true; // 默认显示

  } catch (error) {
    console.error('debug_touch_welfare_abtest_error', '检查 touchToWelfareEnable AB 测试配置失败', error);
    return true; // 出错时默认显示
  }
};

export default getTouchToWelfareEnableABTest;
