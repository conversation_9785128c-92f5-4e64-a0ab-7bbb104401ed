import { NativeModules } from 'react-native';
import { XMAppVersionHelper } from '@xmly/rn-sdk';

/**
 * 检查 touchToWelfareEnable AB 测试配置和版本要求
 * 决定是否显示 ListenTask 模块
 *
 * 检查逻辑：
 * 1. 检查客户端版本是否大于等于 3.3.68
 * 2. 检查 ADABTest.touchToWelfareEnable 属性
 * 3. 两个条件都满足时才显示 ListenTask 模块
 *
 * @returns Promise<boolean> 是否应该显示 ListenTask 模块
 */
export const getTouchToWelfareEnableABTest = async (): Promise<boolean> => {
  console.log('🔍 [ListenTask Debug] 开始检查 ListenTask 显示条件');
  return false;
  // try {
    // 第一步：检查客户端版本
    // let isVersionValid = false;
    // try {
    //   isVersionValid = await XMAppVersionHelper.notLowerThan('3.3.68');
    //   console.log('🔍 [ListenTask Debug] 版本检查结果:', {
    //     requiredVersion: '3.3.68',
    //     isVersionValid,
    //     checkMethod: 'XMAppVersionHelper.notLowerThan'
    //   });
    // } catch (versionError) {
    //   console.error('🔍 [ListenTask Debug] 版本检查失败，设置为 false', versionError);
    //   isVersionValid = false; // 版本检查失败时默认不通过
    // }

    // // 如果版本不满足要求，直接返回 false
    // if (!isVersionValid) {
    //   console.log('🔍 [ListenTask Debug] 版本不满足要求，返回 false');
    //   return false;
    // }

    // // 第二步：检查 AB 测试配置
    // console.log('🔍 [ListenTask Debug] 版本检查通过，开始检查 ADABTest 模块:', {
    //   hasADABTest: !!NativeModules?.ADABTest,
    //   touchToWelfareEnable: NativeModules?.ADABTest?.touchToWelfareEnable,
    //   touchToWelfareEnableType: typeof NativeModules?.ADABTest?.touchToWelfareEnable
    // });

    // if (NativeModules?.ADABTest?.touchToWelfareEnable !== undefined) {
    //   const enableValue = NativeModules.ADABTest.touchToWelfareEnable;
    //   const result = enableValue;
    //   console.log('🔍 [ListenTask Debug] ADABTest 检查结果:', {
    //     enableValue,
    //     enableValueType: typeof enableValue,
    //     finalResult: result
    //   });
    //   return result;
    // }

    // ADABTest 模块不可用时的默认行为
  //   console.log('🔍 [ListenTask Debug] ADABTest 模块不可用，返回 false');
  //   return false; // 默认不显示

  // } catch (error) {
  //   console.error('🔍 [ListenTask Debug] 整体检查失败，返回 false', error);
  //   return false; // 出错时默认不显示
  // }
};

export default getTouchToWelfareEnableABTest;
