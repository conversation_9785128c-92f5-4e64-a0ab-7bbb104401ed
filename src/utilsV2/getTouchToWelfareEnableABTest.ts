import { NativeModules } from 'react-native';
import { XMAppVersionHelper } from '@xmly/rn-sdk';

/**
 * 检查 touchToWelfareEnable AB 测试配置和版本要求
 * 决定是否显示 ListenTask 模块
 *
 * 检查逻辑：
 * 1. 检查客户端版本是否大于等于 3.3.68
 * 2. 检查 ADABTest.touchToWelfareEnable 属性
 * 3. 两个条件都满足时才显示 ListenTask 模块
 *
 * @returns Promise<boolean> 是否应该显示 ListenTask 模块
 */
export const getTouchToWelfareEnableABTest = async (): Promise<boolean> => {
  try {
    // 第一步：检查客户端版本
    let isVersionValid = false;
    try {
      isVersionValid = await XMAppVersionHelper.notLowerThan('3.3.68');
    } catch (versionError) {
      console.error('debug_touch_welfare_version_error', '版本检查失败，默认通过版本检查', versionError);
      isVersionValid = true; // 版本检查失败时默认通过
    }

    // 如果版本不满足要求，直接返回 false
    if (!isVersionValid) {
      return false;
    }

    // 第二步：检查 AB 测试配置
    if (NativeModules?.ADABTest?.touchToWelfareEnable) {
      try {
        const enableValue = await NativeModules.ADABTest.touchToWelfareEnable();
        return !!enableValue;
      } catch (error) {
        console.error('🚀🚀🚀 [LISTEN_TASK_DEBUG] 调用 ADABTest.touchToWelfareEnable() 失败:', error);
        return false;
      }
    }
    // ADABTest 模块不可用时的默认行为
    return false; // 默认显示
  } catch (error) {
    console.error('debug_touch_welfare_abtest_error', '检查 touchToWelfareEnable AB 测试配置失败', error);
    return false; // 出错时默认显示
  }
};

export default getTouchToWelfareEnableABTest;
