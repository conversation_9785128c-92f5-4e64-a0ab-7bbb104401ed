import { NativeModules } from 'react-native';
import { XMAppVersionHelper } from '@xmly/rn-sdk';

/**
 * 检查 touchToWelfareEnable AB 测试配置和版本要求
 * 决定是否显示 ListenTask 模块
 *
 * 检查逻辑：
 * 1. 检查客户端版本是否大于等于 3.3.68
 * 2. 检查 ADABTest.touchToWelfareEnable 属性
 * 3. 两个条件都满足时才显示 ListenTask 模块
 *
 * @returns Promise<boolean> 是否应该显示 ListenTask 模块
 */
export const getTouchToWelfareEnableABTest = async (): Promise<boolean> => {
  try {
    // 第一步：检查客户端版本
    let isVersionValid = false;
    try {
      isVersionValid = await XMAppVersionHelper.notLowerThan('3.3.68');
      console.info('debug_touch_welfare_version_check', '客户端版本检查', {
        requiredVersion: '3.3.68',
        isVersionValid,
        checkMethod: 'XMAppVersionHelper.notLowerThan'
      });
    } catch (versionError) {
      console.error('debug_touch_welfare_version_error', '版本检查失败，默认通过版本检查', versionError);
      isVersionValid = true; // 版本检查失败时默认通过
    }

    // 如果版本不满足要求，直接返回 false
    if (!isVersionValid) {
      console.info('debug_touch_welfare_version_reject', '客户端版本不满足要求，不显示 ListenTask', {
        requiredVersion: '3.3.68',
        result: false
      });
      return false;
    }

    // 第二步：检查 AB 测试配置
    console.info('debug_touch_welfare_abtest_modules', '版本检查通过，检查 ADABTest 模块', {
      ADABTest: !!NativeModules?.ADABTest,
      touchToWelfareEnable: NativeModules?.ADABTest?.touchToWelfareEnable,
      allABModules: Object.keys(NativeModules).filter(key =>
        key.includes('AB') || key.includes('Test')
      )
    });

    // 检查 ADABTest 模块的 touchToWelfareEnable 属性
    if (NativeModules?.ADABTest?.touchToWelfareEnable !== undefined) {
      const enableValue = NativeModules.ADABTest.touchToWelfareEnable;
      const finalResult = !!enableValue;
      console.info('debug_touch_welfare_abtest', '使用 ADABTest.touchToWelfareEnable', {
        value: enableValue,
        type: typeof enableValue,
        versionValid: isVersionValid,
        abTestResult: !!enableValue,
        finalResult
      });
      return finalResult;
    }

    // ADABTest 模块不可用时的默认行为
    console.warn('debug_touch_welfare_abtest_unavailable', 'ADABTest 模块不可用，默认显示 ListenTask', {
      versionValid: isVersionValid,
      availableModules: Object.keys(NativeModules),
      finalResult: true
    });
    return true; // 默认显示

  } catch (error) {
    console.error('debug_touch_welfare_abtest_error', '检查 touchToWelfareEnable AB 测试配置失败', error);
    return true; // 出错时默认显示
  }
};

export default getTouchToWelfareEnableABTest;
