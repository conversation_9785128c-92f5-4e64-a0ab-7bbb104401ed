import { getTouchToWelfareEnableABTest } from '../getTouchToWelfareEnableABTest';

// Mock XMAppVersionHelper
jest.mock('@xmly/rn-sdk', () => ({
  XMAppVersionHelper: {
    notLowerThan: jest.fn()
  }
}));

// Mock NativeModules
jest.mock('react-native', () => ({
  NativeModules: {}
}));

import { XMAppVersionHelper } from '@xmly/rn-sdk';
import { NativeModules } from 'react-native';

describe('getTouchToWelfareEnableABTest', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // 清空 console 方法的 mock
    jest.spyOn(console, 'info').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('版本检查', () => {
    it('应该在版本不满足要求时返回 false', async () => {
      // 模拟版本检查失败
      (XMAppVersionHelper.notLowerThan as jest.Mock).mockResolvedValue(false);

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(false);
      expect(XMAppVersionHelper.notLowerThan).toHaveBeenCalledWith('3.3.68');
    });

    it('应该在版本满足要求且 AB 测试启用时返回 true', async () => {
      // 模拟版本检查通过
      (XMAppVersionHelper.notLowerThan as jest.Mock).mockResolvedValue(true);
      
      // 模拟 ADABTest 模块可用且启用
      (NativeModules as any).ADABTest = {
        touchToWelfareEnable: true
      };

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(true);
      expect(XMAppVersionHelper.notLowerThan).toHaveBeenCalledWith('3.3.68');
    });

    it('应该在版本满足要求但 AB 测试禁用时返回 false', async () => {
      // 模拟版本检查通过
      (XMAppVersionHelper.notLowerThan as jest.Mock).mockResolvedValue(true);
      
      // 模拟 ADABTest 模块可用但禁用
      (NativeModules as any).ADABTest = {
        touchToWelfareEnable: false
      };

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(false);
    });

    it('应该在版本检查失败时默认通过版本检查', async () => {
      // 模拟版本检查抛出异常
      (XMAppVersionHelper.notLowerThan as jest.Mock).mockRejectedValue(new Error('Version check failed'));
      
      // 模拟 ADABTest 模块可用且启用
      (NativeModules as any).ADABTest = {
        touchToWelfareEnable: true
      };

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(true);
    });
  });

  describe('AB 测试检查', () => {
    beforeEach(() => {
      // 默认版本检查通过
      (XMAppVersionHelper.notLowerThan as jest.Mock).mockResolvedValue(true);
    });

    it('应该在 ADABTest 模块不可用时返回 true', async () => {
      // 模拟 ADABTest 模块不存在
      delete (NativeModules as any).ADABTest;

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(true);
    });

    it('应该正确处理 touchToWelfareEnable 为 undefined 的情况', async () => {
      // 模拟 ADABTest 模块存在但 touchToWelfareEnable 未定义
      (NativeModules as any).ADABTest = {};

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(true);
    });

    it('应该正确处理 touchToWelfareEnable 为 0 的情况', async () => {
      // 模拟 touchToWelfareEnable 为 0（falsy 值）
      (NativeModules as any).ADABTest = {
        touchToWelfareEnable: 0
      };

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(false);
    });

    it('应该正确处理 touchToWelfareEnable 为字符串的情况', async () => {
      // 模拟 touchToWelfareEnable 为非空字符串
      (NativeModules as any).ADABTest = {
        touchToWelfareEnable: 'enabled'
      };

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(true);
    });
  });

  describe('错误处理', () => {
    it('应该在整体检查失败时返回 true', async () => {
      // 模拟版本检查通过
      (XMAppVersionHelper.notLowerThan as jest.Mock).mockResolvedValue(true);
      
      // 模拟访问 NativeModules 时抛出异常
      Object.defineProperty(NativeModules, 'ADABTest', {
        get: () => {
          throw new Error('NativeModules access failed');
        }
      });

      const result = await getTouchToWelfareEnableABTest();

      expect(result).toBe(true);
    });
  });
});
